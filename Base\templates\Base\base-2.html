<!DOCTYPE html>
{% load static %}
<html lang="en" class="light-style layout-menu-fixed" dir="ltr" data-theme="theme-default" data-assets-path="{% static 'Base/assets/' %}"
  data-template="vertical-menu-template-free">

<head>
  <meta charset="utf-8" />
  <meta name="viewport"
    content="width=device-width, initial-scale=1.0, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0" />

  <title>{% block title %}{% endblock %}</title>


  <meta name="description" content="" />

  <!-- Favicon -->
  <link rel="icon" type="image/x-icon" href="{% static 'Base/assets/img/favicon/favicon.ico' %}" />

  <!-- Fonts -->
  <link rel="preconnect" href="https://fonts.googleapis.com" />
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
  <link
    href="https://fonts.googleapis.com/css2?family=Public+Sans:ital,wght@0,300;0,400;0,500;0,600;0,700;1,300;1,400;1,500;1,600;1,700&display=swap"
    rel="stylesheet" />

  <!-- Icons. Uncomment required icon fonts -->
  <link rel="stylesheet" href="{% static 'Base/assets/vendor/fonts/boxicons.css' %}" />

  <!-- Core CSS -->
  <link rel="stylesheet" href="{% static 'Base/assets/vendor/css/core.css' %}" class="template-customizer-core-css" />
  <link rel="stylesheet" href="{% static 'Base/assets/vendor/css/theme-default.css' %}"
    class="template-customizer-theme-css" />
  <link rel="stylesheet" href="{% static 'Base/assets/css/demo.css' %}" />

  <!-- Vendors CSS -->
  <link rel="stylesheet" href="{% static 'Base/assets/vendor/libs/perfect-scrollbar/perfect-scrollbar.css' %}" />

  <link rel="stylesheet" href="{% static 'Base/assets/vendor/libs/apex-charts/apex-charts.css' %}" />

  <!-- Page Notificaciones -->
  <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

  <!-- Helpers -->
  <script src="{% static 'Base/assets/vendor/js/helpers.js' %}"></script>

  <!--! Template customizer & Theme config files MUST be included after core stylesheets and helpers.js in the <head> section -->
  <!--? Config:  Mandatory theme config file contain global vars & default theme options, Set your preferred theme option in this file.  -->
  <script src="{% static 'Base/assets/js/config.js' %}"></script>
</head>

<body>
  <!-- Layout wrapper -->

  <div class="layout-wrapper layout-content-navbar">
    <div class="layout-container">
    

   
      <aside id="layout-menu" class="layout-menu menu-vertical menu bg-menu-theme">
          
        <div class="app-brand demo">
          
          <a href="{% url 'Admin' %}" class="app-brand-link">
            <span class="app-brand-logo demo">
              <h4>FRITURAS DE ORIENTE</h4>
            </span>
          </a>
         
          
          

          <a href="javascript:void(0);" class="layout-menu-toggle menu-link text-large ms-auto d-block d-xl-none">
            <i class="bx bx-chevron-left bx-sm align-middle"></i>
          </a>
        </div>
        

        <div class="menu-inner-shadow"></div>

        <ul class="menu-inner py-1">
          <!-- Dashboard -->
          <li class="menu-item active">
            <a href="{% url 'Admin' %}" class="menu-link">
              <i class="menu-icon tf-icons bx bx-home-circle"></i>
              <div data-i18n="Analytics">Inicio</div>
            </a>
          </li>

          <!-- Layouts -->
          <li class="menu-item">
            <a href="javascript:void(0);" class="menu-link menu-toggle">
              <i class="menu-icon tf-icons bx bx-layout"></i>
              <div data-i18n="Layouts">Usuarios</div>
            </a>

            <ul class="menu-sub">
              <li class="menu-item">
                <a href="{% url 'NuevoUser' %}" class="menu-link">
                  <div data-i18n="Without menu">Nuevo Usuario</div>
                </a>
              </li>
              <li class="menu-item">
                <a href="{% url 'ListaUser' %}" class="menu-link">
                  <div data-i18n="Without menu">Lista Usuarios</div>
                </a>
              </li>
            </ul>
          </li>

          <li class="menu-header small text-uppercase">
            <span class="menu-header-text">Administrativos</span>
          </li>
          <li class="menu-item">
            <a href="javascript:void(0);" class="menu-link menu-toggle">
              <i class="menu-icon tf-icons bx bx-lock-open-alt"></i>
              <div data-i18n="Authentications">Tiendas</div>
            </a>
            <ul class="menu-sub">
              <li class="menu-item">
                <a href="{% url 'NuevaTienda' %}" class="menu-link">
                  <div data-i18n="Basic">Nueva Tienda</div>
                </a>
              </li>
              <li class="menu-item">
                <a href="{% url 'ListaTiendas' %}" class="menu-link">
                  <div data-i18n="Basic">Listado Tiendas</div>
                </a>
              </li>
            </ul>
          </li>
          <li class="menu-item">
            <a href="javascript:void(0);" class="menu-link menu-toggle">
              <i class="menu-icon tf-icons bx bx-lock-open-alt"></i>
              <div data-i18n="Authentications">Productos</div>
            </a>
            <ul class="menu-sub">
              <li class="menu-item">
                <a href="{% url 'NuevoProducto' %}" class="menu-link">
                  <div data-i18n="Basic">Nuevo Producto</div>
                </a>
              </li>
              <li class="menu-item">
                <a href="{% url 'ListaProducto' %}" class="menu-link">
                  <div data-i18n="Basic">Listado Productos</div>
                </a>
              </li>
            </ul>
          </li>

          <li class="menu-item">
            <a href="javascript:void(0);" class="menu-link menu-toggle">
              <i class="menu-icon tf-icons bx bx-lock-open-alt"></i>
              <div data-i18n="Authentications">Ventas</div>
            </a>
            <ul class="menu-sub">
              <li class="menu-item">
                <a href="{% url 'NuevaVenta' %}" class="menu-link">
                  <div data-i18n="Basic">Nueva Venta</div>
                </a>
              </li>
              <li class="menu-item">
                <a href="{% url 'ListaVenta' %}" class="menu-link">
                  <div data-i18n="Basic">Listado Ventas</div>
                </a>
              </li>
            </ul>
          </li>

          <li class="menu-item">
            <a href="javascript:void(0);" class="menu-link menu-toggle">
              <i class="menu-icon tf-icons bx bx-lock-open-alt"></i>
              <div data-i18n="Authentications">Cierre</div>
            </a>
            <ul class="menu-sub">
              <li class="menu-item">
                <a href="{% url 'NuevoCierre' %}" class="menu-link">
                  <div data-i18n="Basic">Nuevo Cierre</div>
                </a>
              </li>
              <li class="menu-item">
                <a href="{% url 'ListaCierre' %}" class="menu-link">
                  <div data-i18n="Basic">Listado Cierre</div>
                </a>
              </li>
            </ul>
          </li>

          <li class="menu-item">
            <a href="javascript:void(0);" class="menu-link menu-toggle">
              <i class="menu-icon tf-icons bx bx-lock-open-alt"></i>
              <div data-i18n="Authentications">Facturacion</div>
            </a>
            <ul class="menu-sub">
              <li class="menu-item">
                <a href="{% url 'NuevaFactura' %}" class="menu-link">
                  <div data-i18n="Basic">Nueva Facturacion</div>
                </a>
              </li>
              <li class="menu-item">
                <a href="{% url 'ListaFactura' %}" class="menu-link">
                  <div data-i18n="Basic">Listado Cierre</div>
                </a>
              </li>
            </ul>
          </li>

          <li class="menu-item">
            <a href="javascript:void(0);" class="menu-link menu-toggle">
              <i class="menu-icon tf-icons bx bx-lock-open-alt"></i>
              <div data-i18n="Authentications">Gastos</div>
            </a>
            <ul class="menu-sub">
              <li class="menu-item">
                <a href="{% url 'NuevoGasto' %}" class="menu-link">
                  <div data-i18n="Basic">Nuevo Gasto</div>
                </a>
              </li>
              <li class="menu-item">
                <a href="{% url 'ListaGasto' %}" class="menu-link">
                  <div data-i18n="Basic">Listado Gastos</div>
                </a>
              </li>
            </ul>
          </li>
          
        
            </ul>
          </li>
          
       
       

        </ul>
        
        
       

        </ul>
        
      </aside>
      <!-- Layout container -->
      <div class="layout-page">
        <!-- Navbar -->

        <nav
          class="layout-navbar container-xxl navbar navbar-expand-xl navbar-detached align-items-center bg-navbar-theme"
          id="layout-navbar">
          <div class="layout-menu-toggle navbar-nav align-items-xl-center me-3 me-xl-0 d-xl-none">
            <a class="nav-item nav-link px-0 me-xl-4" href="javascript:void(0)">
              <i class="bx bx-menu bx-sm"></i>
            </a>
          </div>

          <div class="navbar-nav-right d-flex align-items-center" id="navbar-collapse">
            <!-- Search -->
            <div class="navbar-nav align-items-center">
              <div class="nav-item d-flex align-items-center">

              <li class="nav-item lh-1 me-3">
                <a class="github-button" href="#"
                  data-icon="octicon-user" data-size="large" data-show-count="true"
                  aria-label="Star themeselection/sneat-html-admin-template-free on GitHub">
                  ROL: {{user.rol}}
                </a>
              </li>
              </div>
            </div>
            <!-- /Search -->

            <ul class="navbar-nav flex-row align-items-center ms-auto">
              <!-- Place this tag where you want the button to render. -->
              <li class="nav-item lh-1 me-3">
                <a class="github-button" href="#"
                  data-icon="octicon-star" data-size="large" data-show-count="true"
                  aria-label="Star themeselection/sneat-html-admin-template-free on GitHub">
                  Hola! <p style="text-transform: uppercase;">{{user.username}}</p>
                </a>
              </li>
              
              <!-- User -->
              <li class="nav-item navbar-dropdown dropdown-user dropdown">
                <a class="nav-link dropdown-toggle hide-arrow" href="javascript:void(0);" data-bs-toggle="dropdown">
                  <div class="avatar avatar-online">
                    <img src="{% static 'Base/assets/img/avatars/1.png' %}" alt class="w-px-40 h-auto rounded-circle" />
                  </div>
                </a>
                <ul class="dropdown-menu dropdown-menu-end">
                  <li>
                    <a class="dropdown-item" href="#">
                      <div class="d-flex">
                        <div class="flex-shrink-0 me-3">
                          <div class="avatar avatar-online">
                            <img src="{% static 'Base/assets/img/avatars/1.png' %}" alt
                              class="w-px-40 h-auto rounded-circle" />
                          </div>
                        </div>
                        <div class="flex-grow-1">
                          <span class="fw-semibold d-block">{{user.first_name}}</span>
                          <small class="text-muted">{{user.rol}}</small>
                        </div>
                      </div>
                    </a>
                  </li>                  
                  <li>
                    <div class="dropdown-divider"></div>
                  </li>
                  <li>
                    <a class="dropdown-item" href="{% url 'UpdatePass' user.id %}">
                      <i class="bx bx-key me-2"></i>
                      <span class="align-middle">Cambio Password</span>
                    </a>
                  </li>
                  <li>
                    <a class="dropdown-item" href="{% url 'Salir' %}">
                      <i class="bx bx-power-off me-2"></i>
                      <span class="align-middle">Cerrar Sesion</span>
                    </a>
                  </li>
                </ul>
              </li>
              <!--/ User -->
            </ul>
          </div>
        </nav>

        <!-- / Navbar -->
        <!-- / Menu -->

        {% block content %}
        {% endblock %}
        <!-- Footer -->
        <footer class="content-footer footer bg-footer-theme">
          <div class="container-xxl d-flex flex-wrap justify-content-between py-2 flex-md-row flex-column">
            <div class="mb-2 mb-md-0">
              ©
              <script>
                document.write(new Date().getFullYear());
              </script>
              , Diseñado Por
              <a href="#" class="footer-link fw-bolder">MultiServicios Sagastume</a>
            </div>

        </footer>
        <!-- / Footer -->
      </div>
      <!-- / Layout page -->



    </div>

    <!-- Overlay -->
    <div class="layout-overlay layout-menu-toggle"></div>
  </div>
  <!-- / Layout wrapper -->



  <!-- Core JS -->
  <!-- build:js assets/vendor/js/core.js -->
  <script src="{% static 'Base/assets/vendor/libs/jquery/jquery.js' %}"></script>
  <script src="{% static 'Base/assets/vendor/libs/popper/popper.js' %}"></script>
  <script src="{% static 'Base/assets/vendor/js/bootstrap.js' %}"></script>
  <script src="{% static 'Base/assets/vendor/libs/perfect-scrollbar/perfect-scrollbar.js' %}"></script>

  <script src="{% static 'Base/assets/vendor/js/menu.js' %}"></script>
  <!-- endbuild -->

  <!-- Vendors JS -->
  <script src="{% static 'Base/assets/vendor/libs/apex-charts/apexcharts.js' %}"></script>

  <!-- Main JS -->
  <script src="{% static 'Base/assets/js/main.js' %}"></script>

  <!-- Page JS -->
  <script src="{% static 'Base/assets/js/dashboards-analytics.js' %}"></script>

  <!-- Place this tag in your head or just before your close body tag. -->
  <script async defer src="https://buttons.github.io/buttons.js"></script>
  
</body>

</html>