from django import forms
from .models import Producto, IngresoProducto

UNIDAD = (
    ('<PERSON>co','Saco'),
    ('Li<PERSON>','Libra'),
    ('<PERSON><PERSON>','<PERSON><PERSON>'),
    ('<PERSON><PERSON>a','<PERSON>lsa'),
    ('<PERSON><PERSON>','Far<PERSON>'),
    ('Tira','Tira'),
    ('Galon','Galon'),
    ('Unidades','Unidades'),
    ('<PERSON><PERSON><PERSON>','Paquete'),
)

class ProductoForm(forms.ModelForm):
    class Meta:
        model = Producto
        fields = ['nombre', 'medida', 'precio_compra', 'precio_venta', 'id_cate', 'tienda', 'imagen']

        widgets = {
            'nombre': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Nombre del Producto',
                'autofocus': True
            }),
            'medida': forms.Select(
                attrs={'class': 'form-select'},
                choices=UNIDAD
            ),
            'precio_compra': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.01'
            }),
            'precio_venta': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.01'
            }),
            'id_cate': forms.Select(attrs={'class': 'form-select'}),
            'tienda': forms.Select(attrs={'class': 'form-select'}),
            'imagen': forms.FileInput(attrs={'class': 'form-control', 'accept': 'image/*'}),
        }

class UpdateProductoForm(forms.ModelForm):
    class Meta:
        model = Producto
        fields = ['nombre', 'medida', 'precio_compra', 'precio_venta', 'id_cate', 'tienda', 'estado', 'imagen']

        widgets = {
            'nombre': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Nombre del Producto'
            }),
            'medida': forms.Select(
                attrs={'class': 'form-select'},
                choices=UNIDAD
            ),
            'precio_compra': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.01'
            }),
            'precio_venta': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.01'
            }),
            'id_cate': forms.Select(attrs={'class': 'form-select'}),
            'tienda': forms.Select(attrs={'class': 'form-select'}),
            'estado': forms.CheckboxInput(attrs={
                'class': 'form-check-input'
            }),
            'imagen': forms.FileInput(attrs={'class': 'form-control', 'accept': 'image/*'}),
        }

class IngresoProductoForm(forms.ModelForm):
    class Meta:
        model = IngresoProducto
        fields = ['cantidad', 'precio_compra', 'observacion']

        widgets = {
            'cantidad': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': '1'
            }),
            'precio_compra': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.01'
            }),
            'observacion': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3
            })
        }