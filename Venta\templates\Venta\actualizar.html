{% extends 'Base/base.html' %}
{% load static %}


{% block content %}

{% if messages %}
{% for message in messages %}
<script>
  Swal.fire({
    "title": "Informacion Sistema",
    "text": "{{message}}",
    "icon": "{{message.tags}}"
  })
</script>
{% endfor %}
{% endif %}


<div class="container">
  <h3>FORMULARIO DE ACTUALIZACION VENTAS</h3>
  <form action="#" method="POST" enctype="multipart/form-data">{% csrf_token %}
  <div class="row">

    <div class="col-md-4">
      <label>{{form.nombre.label_tag}}</label>
      {{form.nombre}}
    </div>
    <div class="col-md-4">
      <label>{{form.descripcion.label_tag}}</label>
      {{form.descripcion}}
    </div>
    <div class="col-md-4">
      <label>{{form.factura.label_tag}}</label>
      {{form.factura}}
    </div>


  </div><br>

  <div class="row">

    <div class="col-md-4">
      <label>{{form.cantidad.label_tag}}</label>
      {{form.cantidad}}
    </div>
    <div class="col-md-4">
      <label>{{form.precio.label_tag}}</label>
      {{form.precio}}
    </div>
    <div class="col-md-4">
      <label>{{form.tienda.label_tag}}</label>
      {{form.tienda}}
    </div>


  </div><br>

  <div class="row">

    <div class="col-md-4">
      <button type="submit" class="btn btn-success">Actualizar</button>
      <a href="{% url 'Admin' %}" class="btn btn-danger">Cancelar</a>
    </div>

  </div><br>
  </form>
</div>




{% endblock %}