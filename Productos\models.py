from django.db import models
from Categoria.models import Categoria
from user.models import User
from Dash.models import Tienda

class Producto(models.Model):
    nombre = models.CharField(max_length=550)
    medida = models.CharField(max_length=550)
    stock = models.IntegerField(default=0)  # Existencias actuales
    precio_compra = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    precio_venta = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    estado = models.BooleanField(default=True)  # True = Activo, False = Baja
    id_cate = models.ForeignKey(Categoria, on_delete=models.CASCADE)
    tienda = models.ForeignKey(Tienda, on_delete=models.CASCADE)
    fecha_creacion = models.DateTimeField(auto_now_add=True)
    fecha_modificacion = models.DateTimeField(auto_now=True)
    usuario = models.ForeignKey(User, on_delete=models.CASCADE)
    imagen = models.ImageField(
        upload_to='productos/', 
        null=True, 
        blank=True,
        help_text="Formatos soportados: JPG, PNG, SVG"
    )

    class Meta:
        ordering = ["id"]
        unique_together = ['nombre', 'medida', 'tienda']

    def __str__(self):
        return f"{self.nombre} - {self.tienda.nombre}"

class IngresoProducto(models.Model):
    producto = models.ForeignKey(Producto, on_delete=models.CASCADE)
    cantidad = models.IntegerField()
    precio_compra = models.DecimalField(max_digits=12, decimal_places=2)
    fecha = models.DateTimeField(auto_now_add=True)
    usuario = models.ForeignKey(User, on_delete=models.CASCADE)
    observacion = models.TextField(blank=True, null=True)

    def save(self, *args, **kwargs):
        # Actualizar stock del producto
        self.producto.stock += self.cantidad
        self.producto.precio_compra = self.precio_compra
        self.producto.save()
        super().save(*args, **kwargs)





