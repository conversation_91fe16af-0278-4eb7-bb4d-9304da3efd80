{% extends 'Base/base.html' %}
{% load static %}

{% block content %}

<!--{% if messages %}
  {% for message in messages %}
    <script>
      Swal.fire({
        "title": "Informacion Sistema",
        "text": "{{message}}",
        "icon": "{{message.tags}}"
      })
    </script>
  {% endfor %}
{% endif %}-->

<div class="container" style="margin-top: 75px;">
  <h4>LISTADO DE PRODUCTOS</h4><br>

  <!-- Formulario para seleccionar la tienda -->
  <form method="get" id="tiendaForm" action="{% if tienda_seleccionada %}{% url 'productos_por_tienda' tienda_seleccionada %}{% else %}{% url 'ListaProducto' %}{% endif %}">

    <div class="row">
      <div class="col-md-6">
        <label for="tienda">Selecciona una tienda:</label>
        <select class="form-control" name="tienda" id="tienda" onchange="this.form.submit()">
          <option value="">-- Todas las tiendas --</option>
          {% for tienda in tiendas %}
            <option value="{{ tienda.id }}" {% if tienda.id == tienda_seleccionada %}selected{% endif %}>
              {{ tienda.nombre }}
            </option>
          {% endfor %}
        </select>
      </div>
      <div class="col-md-6 d-flex align-items-end">
        <button type="submit" class="btn btn-primary">Ingresar</button>
        <a href="{% url 'ListaProducto' %}" class="btn btn-secondary ml-2">Ver Todos</a>
      </div>
    </div>
  </form>
  <br>

  <div class="table-responsive">
    <input class="form-control col-md-3 light-table-filter" data-table="order-table" type="text"
           placeholder="Buscar.." style="border: 1px solid black;" name="buscar" autofocus="buscar"><br>
    <table class="table table-bordered table-hover order-table" style="height: 5rem; overflow-y: scroll;">
      <thead>
        <tr>
          <td>Producto</td>
          <td>Unidad Medida</td>
          <td>Inicio</td>
          <td>Ingreso</td>
          <td>Salida</td>
          <td>Final Dia Anterior</td>
          <td>Tienda</td>
          <td>Acciones</td>
        </tr>
      </thead>
      <tbody>
        {% for p in p %}
          <tr>
            <td>{{ p.nombre }}</td>
            <td>{{ p.medida }}</td>
            <td>{{ p.inicio }}</td>
            <td>{{ p.ingreso }}</td>
            <td>{{ p.salida }}</td>
            <td>{{ p.final }}</td>
            <td>{{ p.tienda.nombre }}</td>
            <td>
              <a href="{% url 'UpdateProducto' p.id %}">Actualizar</a>
              <a href="{% url 'DeleteProducto' p.id %}" style="color: red;">Eliminar</a>
            </td>
          </tr>
        {% empty %}
          <tr>
            <td colspan="8">SIN PRODUCTOS</td>
          </tr>
        {% endfor %}
      </tbody>
    </table>
  </div>
</div>

<script type="text/javascript">
  (function (document) {
      'use strict';

      var LightTableFilter = (function (Arr) {

          var _input;

          function _onInputEvent(e) {
              _input = e.target;
              var tables = document.getElementsByClassName(_input.getAttribute('data-table'));
              Arr.forEach.call(tables, function (table) {
                  Arr.forEach.call(table.tBodies, function (tbody) {
                      Arr.forEach.call(tbody.rows, _filter);
                  });
              });
          }

          function _filter(row) {
              var text = row.textContent.toLowerCase(), val = _input.value.toLowerCase();
              row.style.display = text.indexOf(val) === -1 ? 'none' : 'table-row';
          }

          return {
              init: function () {
                  var inputs = document.getElementsByClassName('light-table-filter');
                  Arr.forEach.call(inputs, function (input) {
                      input.oninput = _onInputEvent;
                  });
              }
          };
      })(Array.prototype);

      document.addEventListener('readystatechange', function () {
          if (document.readyState === 'complete') {
              LightTableFilter.init();
          }
      });

  })(document);
</script>

{% endblock %}
