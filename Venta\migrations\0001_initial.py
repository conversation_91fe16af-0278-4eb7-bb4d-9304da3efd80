# Generated by Django 5.0.4 on 2024-11-10 23:51

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('Dash', '0001_initial'),
        ('Productos', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Venta',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('cantidad', models.IntegerField(default=0)),
                ('precio', models.DecimalField(decimal_places=2, default=0.0, max_digits=12)),
                ('total', models.DecimalField(decimal_places=2, default=0.0, max_digits=12)),
                ('estado', models.IntegerField(default=1)),
                ('fecha', models.DateTimeField(auto_now_add=True)),
                ('fecha_mod', models.DateTimeField(auto_now_add=True)),
                ('prod', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='Productos.producto')),
                ('tienda', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='Dash.tienda')),
                ('usuario', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['id'],
            },
        ),
    ]
