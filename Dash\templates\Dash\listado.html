{% extends 'Base/base.html' %}
{% load static %}


{% block content %}

{% if messages %}
{% for message in messages %}
<script>
  Swal.fire({
    "title": "Informacion Sistema",
    "text": "{{message}}",
    "icon": "{{message.tags}}"
  })
</script>
{% endfor %}
{% endif %}


<div class="container" style="margin-top: 75px;">
  <h4>LISTADO DE TIENDAS</h4><br>
  <div class="table-responsive">
    <input class="form-control col-md-3 light-table-filter" data-table="order-table" type="text"
            placeholder="Buscar.." style="border: 1px solid black;" name="buscar" autofocus="buscar"><br>
    <table class="table table-borderede table-hover order-table" style="height: 5rem; overflow-y: scroll;">

      <thead>
        <tr>
          <td>Nombre</td>
          <td>Estado</td>
          <td>Actions</td>
        </tr>
      </thead>

      <tbody>
        {% for p in p %}
        <tr>
          <td>{{p.nombre}}</td>
          {% if p.estado == 1 %}
          <td>Activa</td>
          {% else %}
          <td>No Activa</td>
          {% endif %}
          <td>
            <a href="{% url 'UpdateTienda' p.id %}">Actualizar</a>
            <a href="{% url 'DeleteTienda' p.id %}" style="color: red;">Eliminar</a>

          </td>
        </tr>
        {% empty %}
        <caption>SIN TIENDAS</caption>
        {% endfor %}
      </tbody>


    </table>

  </div>


</div>


<script type="text/javascript">
  (function (document) {
      'use strict';

      var LightTableFilter = (function (Arr) {

          var _input;

          function _onInputEvent(e) {
              _input = e.target;
              var tables = document.getElementsByClassName(_input.getAttribute('data-table'));
              Arr.forEach.call(tables, function (table) {
                  Arr.forEach.call(table.tBodies, function (tbody) {
                      Arr.forEach.call(tbody.rows, _filter);
                  });
              });
          }

          function _filter(row) {
              var text = row.textContent.toLowerCase(), val = _input.value.toLowerCase();
              row.style.display = text.indexOf(val) === -1 ? 'none' : 'table-row';
          }

          return {
              init: function () {
                  var inputs = document.getElementsByClassName('light-table-filter');
                  Arr.forEach.call(inputs, function (input) {
                      input.oninput = _onInputEvent;
                  });
              }
          };
      })(Array.prototype);

      document.addEventListener('readystatechange', function () {
          if (document.readyState === 'complete') {
              LightTableFilter.init();
          }
      });

  })(document);
</script>

{% endblock %}