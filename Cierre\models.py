from django.db import models
from user.models import User
from Dash.models import Tienda

class Cierre(models.Model):
    ventas = models.DecimalField(max_digits=12,decimal_places=2,blank=False,null=False,default=0.00)
    gastos = models.DecimalField(max_digits=12,decimal_places=2,blank=False,null=False,default=0.00)
    caja = models.DecimalField(max_digits=12,decimal_places=2,blank=False,null=False,default=0.00)
    deposito = models.DecimalField(max_digits=12,decimal_places=2,blank=False,null=False,default=0.00)
    boleta = models.CharField(max_length=50,blank=False,null=False,default='0')
    pollo = models.DecimalField(max_digits=12,decimal_places=2,blank=False,null=False,default=0.00)
    boleta_pollo = models.CharField(max_length=50,blank=False,null=False,default='0')
    libras = models.DecimalField(max_digits=12,decimal_places=2,blank=False,null=False,default=0.00)
    liquido = models.DecimalField(max_digits=12,decimal_places=2,blank=False,null=False,default=0.00)
    tienda = models.ForeignKey(Tienda,blank=False,null=False,on_delete=models.CASCADE)
    fecha = models.DateField(blank=False,null=False,auto_now_add=True)
    fecha_mod = models.DateTimeField(blank=False,null=False,auto_now_add=True)
    usuario = models.ForeignKey(User,blank=False,null=False,on_delete=models.CASCADE)
    estado = models.IntegerField(blank=False,null=False,default=1)

    class Meta:
        ordering = ["id"]

    def __str__(self):
        return str(self.id)




