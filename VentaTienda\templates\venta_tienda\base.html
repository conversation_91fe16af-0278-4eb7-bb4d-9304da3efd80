<!DOCTYPE html>
<html lang="es">
<head>



    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Sistema de Ventas{% endblock %}</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <style>
        body {
            padding-top: 20px;
            background: #f8f9fa;
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .container {
            position: relative;
            z-index: 1;
        }

        .card-menu {
            transition: all 0.3s ease;
            height: 100%;
            cursor: pointer;
            border: none;
            border-radius: 15px;
            overflow: hidden;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
        }

        .card-menu:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow: 0 20px 40px rgba(0,0,0,0.2);
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
        }

        .btn {
            border-radius: 10px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .btn-action {
            font-size: 1.2rem;
            padding: 15px;
            margin: 5px;
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: flex-start;
            border-radius: 12px;
        }

        .btn-action i {
            margin-right: 10px;
            font-size: 1.5rem;
        }
        .categoria-item {
            cursor: pointer;
            padding: 15px;
            margin: 8px 0;
            border-radius: 12px;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.8);
            border: 2px solid transparent;
        }

        .categoria-item:hover {
            background: rgba(255, 255, 255, 1);
            border-color: #007bff;
            transform: translateX(5px);
            box-shadow: 0 5px 15px rgba(0,123,255,0.2);
        }

        .categoria-item.active {
            background: linear-gradient(45deg, #007bff, #0056b3);
            color: white;
            border-color: #0056b3;
            transform: translateX(5px);
            box-shadow: 0 5px 15px rgba(0,123,255,0.3);
        }

        .producto-card {
            cursor: pointer;
            transition: all 0.3s ease;
            height: 100%;
            border-radius: 15px;
            overflow: hidden;
            background: rgba(255, 255, 255, 0.95);
            border: none;
        }

        .producto-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 15px 30px rgba(0,0,0,0.2);
        }

        .producto-img {
            height: 150px;
            object-fit: cover;
            transition: transform 0.3s ease;
        }

        .producto-card:hover .producto-img {
            transform: scale(1.1);
        }
        .cart-table {
            font-size: 1.1rem;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 10px;
        }

        .btn-remove {
            color: #dc3545;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-remove:hover {
            color: #fff;
            background: #dc3545;
            border-radius: 50%;
            transform: scale(1.2);
        }

        .badge {
            border-radius: 20px;
            font-size: 0.8rem;
            padding: 5px 10px;
        }

        .alert {
            border-radius: 12px;
            border: none;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }

        .form-control {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
            transform: translateY(-2px);
        }

        .modal-content {
            border-radius: 15px;
            border: none;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }

        .modal-header {
            border-bottom: 1px solid rgba(0,0,0,0.1);
            border-radius: 15px 15px 0 0;
        }

        .modal-footer {
            border-top: 1px solid rgba(0,0,0,0.1);
            border-radius: 0 0 15px 15px;
        }

        /* Animaciones personalizadas */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .fade-in-up {
            animation: fadeInUp 0.6s ease-out;
        }

        /* Responsive improvements */
        @media (max-width: 768px) {
            .card-menu {
                margin-bottom: 20px;
            }

            .btn-action {
                font-size: 1rem;
                padding: 12px;
            }

            .producto-card {
                margin-bottom: 15px;
            }
        }

        {% block extra_css %}{% endblock %}
    </style>
</head>
<body>
    <div class="container-fluid">
        {% block content %}{% endblock %}
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    {% block extra_js %}{% endblock %}
</body>
</html>