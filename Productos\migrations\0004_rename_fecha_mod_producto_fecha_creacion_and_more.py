# Generated by Django 4.2.16 on 2025-05-17 22:34

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('Categoria', '0002_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('Dash', '0001_initial'),
        ('Productos', '0003_producto_precio_compra_producto_precio_venta_and_more'),
    ]

    operations = [
        migrations.RenameField(
            model_name='producto',
            old_name='fecha_mod',
            new_name='fecha_creacion',
        ),
        migrations.RenameField(
            model_name='producto',
            old_name='final',
            new_name='stock',
        ),
        migrations.AddField(
            model_name='producto',
            name='fecha_modificacion',
            field=models.DateTimeField(auto_now=True),
        ),
        migrations.AddField(
            model_name='producto',
            name='id_cate',
            field=models.ForeignKey(default=1, on_delete=django.db.models.deletion.CASCADE, to='Categoria.categoria'),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name='producto',
            name='estado',
            field=models.BooleanField(default=True),
        ),
        migrations.AlterField(
            model_name='producto',
            name='medida',
            field=models.CharField(max_length=550),
        ),
        migrations.AlterField(
            model_name='producto',
            name='nombre',
            field=models.CharField(max_length=550),
        ),
        migrations.AlterField(
            model_name='producto',
            name='precio_compra',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=12),
        ),
        migrations.AlterField(
            model_name='producto',
            name='precio_venta',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=12),
        ),
        migrations.AlterUniqueTogether(
            name='producto',
            unique_together={('nombre', 'medida', 'tienda')},
        ),
        migrations.CreateModel(
            name='IngresoProducto',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('cantidad', models.IntegerField()),
                ('precio_compra', models.DecimalField(decimal_places=2, max_digits=12)),
                ('fecha', models.DateTimeField(auto_now_add=True)),
                ('observacion', models.TextField(blank=True, null=True)),
                ('producto', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='Productos.producto')),
                ('usuario', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.RemoveField(
            model_name='producto',
            name='fecha',
        ),
        migrations.RemoveField(
            model_name='producto',
            name='ingreso',
        ),
        migrations.RemoveField(
            model_name='producto',
            name='inicio',
        ),
        migrations.RemoveField(
            model_name='producto',
            name='salida',
        ),
        migrations.RemoveField(
            model_name='producto',
            name='total_venta',
        ),
    ]
