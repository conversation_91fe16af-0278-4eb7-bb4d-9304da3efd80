{% extends 'Base/base.html' %}

{% block title %}Lista Recetas{% endblock %}

{% block content %}

<br></br>
<div class="container">

    <a href="{% url 'NuevaReceta' %}" class="btn btn-info">Nueva Receta</a>
    <a href="{% url 'ListaReceta2' %}" class="btn btn-danger">Receta sin Uso</a>

    <div class="col-md-12">
        {% if messages %}
        {% for message in messages %}
        <script>

            Swal.fire({
                title: 'He<PERSON>!',
                text: '{{message}}',
                icon: 'success',
                confirmButtonText: 'Ok'
            })

        </script>
        {% endfor %}
        {% endif %}
    </div>
</div>

<div class="row">
    <div class="container">
        <div class="col-md-12">
            <div class="card my-4">
                <div class="card-header p-0 position-relative mt-n4 mx-3 z-index-2">
                    <div class="bg-gradient-primary shadow-primary border-radius-lg pt-4 pb-3"><br>
                        <h3 class="text-black text-capitalize ps-3">Listado de Receta</h3>
                    </div>
                </div>
                <div class="card-body px-0 pb-2">
                    <div class="table-responsive p-0">
                        <table class="table align-items-center mb-0">
                            <thead>
                                <tr>
                                    <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">
                                        Id Receta</th>
                                    <th
                                        class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7 ps-2">
                                        Nombre</th>
                                    <th
                                        class="text-uppercase text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">
                                        Descripcion</th>
                                    <th
                                        class="text-uppercase text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">
                                        Tiempo(dias)</th>
                                    <th
                                        class="text-uppercase text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">
                                        Total</th>
                                        <th
                                        class="text-uppercase text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">
                                        Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for s in lista %}
                                <tr>
                                    <td>
                                        <div class="d-flex px-2 py-1">
                                            <div class="d-flex flex-column justify-content-center">
                                                <h6 class="mb-0 text-sm">{{s.id}}</h6>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <p class="text-xs font-weight-bold mb-0">{{s.nombre}}</p>
                                    </td>
                                    <td>
                                        <p class="text-xs font-weight-bold mb-0">{{s.descripcion}}</p>
                                    </td>
                                    <td>
                                        <p class="text-xs font-weight-bold mb-0">{{s.tiempo}}</p>
                                    </td>
                                    <td>
                                        <p class="text-xs font-weight-bold mb-0">Q.{{s.precio_receta}}</p>
                                    </td>
                                    <td class="align-middle">
                                        <a href="{% url 'AplicarReceta' s.token %}">
                                              <i style="font-size: 25px; color:green;" class='fa fa-check'
                                            title="Modificar"></i>
                                        </a>
                                        <a href="{% url 'DetalleReceta' s.token %}">
                                       <i style="font-size: 25px; color:rgb(214, 201, 84);" class='fa fa-edit'
                                            title="Modificar"></i>
                                        </a>
                                        <a href="{% url 'BajaReceta' s.id %}">
                                            <i style="font-size: 25px; color:red" class='fa fa-arrow-down '
                                                title="Modificar"></i>
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

</div>


{% endblock %}