{% extends 'Base/base.html' %}
{% load static %}

{% block title %}{{ titulo }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bx bx-user-plus"></i> {{ titulo }}
                    </h5>
                </div>
                
                <div class="card-body">
                    <form method="POST">
                        {% csrf_token %}
                        
                        <div class="row">
                            <!-- Información de Usuario -->
                            <div class="col-12">
                                <h6 class="text-muted mb-3">
                                    <i class="bx bx-user"></i> Información de Usuario
                                </h6>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.username.id_for_label }}" class="form-label">
                                    {{ form.username.label }} <span class="text-danger">*</span>
                                </label>
                                {{ form.username }}
                                {% if form.username.errors %}
                                    <div class="text-danger small">
                                        {{ form.username.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.password.id_for_label }}" class="form-label">
                                    {{ form.password.label }} <span class="text-danger">*</span>
                                </label>
                                {{ form.password }}
                                {% if form.password.errors %}
                                    <div class="text-danger small">
                                        {{ form.password.errors.0 }}
                                    </div>
                                {% endif %}
                                {% if empleado %}
                                    <small class="text-muted">Dejar en blanco para mantener la contraseña actual</small>
                                {% endif %}
                            </div>
                        </div>
                        
                        <hr>
                        
                        <div class="row">
                            <!-- Información Personal -->
                            <div class="col-12">
                                <h6 class="text-muted mb-3">
                                    <i class="bx bx-id-card"></i> Información Personal
                                </h6>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.nombre.id_for_label }}" class="form-label">
                                    {{ form.nombre.label }} <span class="text-danger">*</span>
                                </label>
                                {{ form.nombre }}
                                {% if form.nombre.errors %}
                                    <div class="text-danger small">
                                        {{ form.nombre.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.apellido.id_for_label }}" class="form-label">
                                    {{ form.apellido.label }} <span class="text-danger">*</span>
                                </label>
                                {{ form.apellido }}
                                {% if form.apellido.errors %}
                                    <div class="text-danger small">
                                        {{ form.apellido.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.email.id_for_label }}" class="form-label">
                                    {{ form.email.label }}
                                </label>
                                {{ form.email }}
                                {% if form.email.errors %}
                                    <div class="text-danger small">
                                        {{ form.email.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.telefono.id_for_label }}" class="form-label">
                                    {{ form.telefono.label }}
                                </label>
                                {{ form.telefono }}
                                {% if form.telefono.errors %}
                                    <div class="text-danger small">
                                        {{ form.telefono.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.dpi.id_for_label }}" class="form-label">
                                    {{ form.dpi.label }} <span class="text-danger">*</span>
                                </label>
                                {{ form.dpi }}
                                {% if form.dpi.errors %}
                                    <div class="text-danger small">
                                        {{ form.dpi.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.tienda.id_for_label }}" class="form-label">
                                    {{ form.tienda.label }} <span class="text-danger">*</span>
                                </label>
                                {{ form.tienda }}
                                {% if form.tienda.errors %}
                                    <div class="text-danger small">
                                        {{ form.tienda.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <hr>
                        
                        <div class="row">
                            <div class="col-12">
                                <div class="d-flex justify-content-between">
                                    <a href="{% url 'admin_empleados:lista' %}" class="btn btn-secondary">
                                        <i class="bx bx-arrow-back"></i> Cancelar
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="bx bx-save"></i> {{ accion }}
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Validación del formulario
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('form');
    const usernameField = document.getElementById('{{ form.username.id_for_label }}');
    const dpiField = document.getElementById('{{ form.dpi.id_for_label }}');
    
    // Validar que el username no tenga espacios
    usernameField.addEventListener('input', function() {
        this.value = this.value.replace(/\s/g, '');
    });
    
    // Validar que el DPI solo tenga números
    dpiField.addEventListener('input', function() {
        this.value = this.value.replace(/\D/g, '');
        if (this.value.length > 13) {
            this.value = this.value.slice(0, 13);
        }
    });
});
</script>
{% endblock %}
