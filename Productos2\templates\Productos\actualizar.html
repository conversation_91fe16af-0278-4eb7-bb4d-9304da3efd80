{% extends 'Base/base.html' %}
{% load static %}

{% block content %}

<!--{% if messages %}
{% for message in messages %}
<script>
  Swal.fire({
    "title": "Informacion Sistema",
    "text": "{{ message }}",
    "icon": "{{ message.tags }}"
  })
</script>
{% endfor %}
{% endif %}-->

<div class="container">
  <h3>FORMULARIO DE ACTUALIZACION PRODUCTOS</h3>
  <form action="#" method="POST" enctype="multipart/form-data">
    {% csrf_token %}
    <div class="row">
      <div class="col-md-4">
        <label>{{ form.nombre.label_tag }}</label>
        {{ form.nombre }}
      </div>
      <div class="col-md-4">
        <label>{{ form.medida.label_tag }}</label>
        {{ form.medida }}
      </div>
      <div class="col-md-4">
        <label>{{ form.inicio.label_tag }}</label>
        {{ form.inicio }}
      </div>
    </div><br>

    <div class="row">
      <div class="col-md-3">
        <label>{{ form.ingreso.label_tag }}</label>
        {{ form.ingreso }}
      </div>
      <div class="col-md-3">
        <label>{{ form.salida.label_tag }}</label>
        {{ form.salida }}
      </div>
      <div class="col-md-3">
        <label>{{ form.final.label_tag }}</label>
        {{ form.final }}
      </div>
      <div class="col-md-3">
        <label>{{ form.tienda.label_tag }}</label>
        {{ form.tienda }}
      </div>
      
      <div class="col-md-4">
        <label>{{ form.estado.label_tag }}</label>
        {{ form.estado }}
      </div>
    </div><br>

    <div class="row">
      <div class="col-md-4">
        <button type="submit" class="btn btn-success">Guardar</button>
        <!-- Asegúrate de incluir el parámetro 'tienda' en el enlace de 'Cancelar' -->
        <a href="{% url 'ListaProducto' %}?tienda={{ tienda_seleccionada }}" class="btn btn-danger">Cancelar</a>
      </div>
    </div><br>
  </form>
</div>

<script>
  let precio1 = document.getElementById("inicio")
  let precio2 = document.getElementById("ingreso")
  let precio3 = document.getElementById("salida")
  let precio4 = document.getElementById("final")
  
  precio3.addEventListener("change", () => {
      precio4.value = (parseFloat(precio1.value) + parseFloat(precio2.value))- parseFloat(precio3.value)
  })
</script>

{% endblock %}
