{% extends 'Base/base.html' %}

{% block title %}Detalle Receta{% endblock %}

{% block content %}
<br>
<div class="container">    

    <a href="{% url 'ListaReceta' %}" class="btn btn-primary">Regresar</a>
  </div>

  <div class="container" style="background-color: dark; margin-top: 5px; max-width: 90%;"><br>
    <div class="card my-4">
<div class="container" style="background-color: white;"><br>
    <div class="row">
        <div class="col-md-6">
            <h3>FORMULARIO INGRESO MATERIA PARA RECETA</h3>
        </div>
 
    </div>

    {% if messages %}
    {% for message in messages %}
    <script>

        Swal.fire({
            title: 'Hecho!',
            text: '{{message}}',
            icon: 'success',
            confirmButtonText: 'Ok'
        })

    </script>
    {% endfor %}
    {% endif %}

    <form action="#" method="POST" enctype="multipart/form-data">
        {% csrf_token %}
        <div class="row">
            <div class="col-md-3">
                <div class="col-md-12">
                    <label>Receta</label>
                    <input type="text" class="form-control" value="{{r.nombre}}" readonly required>
                </div>
            </div>
            <div class="col-md-3">
                <div class="col-md-12">
                    <label>Tienda</label>
                    <input type="text" class="form-control" value="{{ r.tienda }}" readonly required>
                </div>
            </div>
            <div class="col-md-3">
                <div class="col-md-12">
                    <label>Fecha</label>
                    <input type="text" class="form-control" name="fecha" value="{% now 'd-m-Y' %}" readonly>
                </div>
            </div>
            <div class="col-md-3">
                <div class="col-md-12">
                    <label>Usuarioz</label>
                    <input type="text" class="form-control" name="usuario" value="{{user.username}}" readonly>
                </div>
            </div>
        
        </div>
        <br><br>
        <div class="row">
            <div class="col-md-3">
                <div class="col-md-12">
                    <button type="button" class="btn btn-primary" onclick="abrirBuscadorProductos()">
                        Nuevo Producto
                    </button>
                </div>
            </div>

            <!-- Campos ocultos para el formulario -->
            <input type="hidden" id="producto_id" name="producto">
            <input type="hidden" id="cantidad_producto" name="cantidad">
                        
        </div><br>
        <div class="row">
            <div class="col-md-4">
                <button type="reset" class="btn btn-danger">Cancelar</button>
            </div>
            <div class="col-md-4">

            </div>
            <div class="col-md-4" align="right">
                <a href="{% url 'NuevaReceta' %}" class="btn btn-warning">Terminar</a>
            </div>
        </div><br>
    </form>

    <hr>

    <div class="container">

        <div class="table-responsive">

            <table class="table table-bordered table-hover table-sm">

                <thead>
                    <tr>
                        <td>Codigo</td>
                        <td>Producto</td>
                        <td>Cantidad</td>
                        <td>Precio</td>
                        <td>Total</td>
                        <td>Actions</td>
                    </tr>
                </thead>

                <tbody>
                    {% for d in d %}
                    <tr>
                        <td>{{d.producto.id}}</td>
                        <td>{{d.producto.nombre}}</td>
                        <td>{{d.cantidad}}</td>
                        <td>Q.{{d.precio_materia}}</td>
                        <td>Q.{{d.total}}</td>
                        <td>
                            <form action="#" method="POST">{% csrf_token %}
                            <input type="hidden" name="corr" value="{{d.id}}">
                            <button name="quitar" class="text-danger">Quitar</button>
                            </form>
                        </td>
                    </tr>
                    {% empty %}
                    <caption>SIN PRODUCTOS</caption>
                    {% endfor %}
                </tbody>


            </table><br>

        </div>

    </div>

</div>
<script>
    function abrirBuscadorProductos() {
        Swal.fire({
            title: 'Seleccionar Producto',
            html: `
                <div class="container">
                    <div class="form-group mb-3">
                        <input type="text" id="searchInput" class="form-control" placeholder="Buscar producto...">
                    </div>
                    <div class="table-responsive" style="max-height: 300px;">
                        <table class="table table-bordered table-hover" id="productosTable">
                            <thead>
                                <tr>
                                    <th>Código</th>
                                    <th>Nombre</th>
                                    <th>Seleccionar</th>
                                </tr>
                            </thead>
                            <tbody id="productosBody">
                            </tbody>
                        </table>
                    </div>
                </div>
            `,
            width: '50%',
            showCloseButton: true,
            showConfirmButton: false,
            didOpen: () => {
                cargarProductos();
                document.getElementById('searchInput').addEventListener('keyup', function() {
                    filtrarProductos(this.value);
                });
            }
        });
    }
    
    function cargarProductos() {
        // Hacer la petición AJAX para obtener los productos
        fetch('{% url "obtener_productos" tienda_id=r.tienda.id %}')
            .then(response => response.json())
            .then(productos => {
                const tbody = document.getElementById('productosBody');
                tbody.innerHTML = '';
                productos.forEach(producto => {
                    tbody.innerHTML += `
                        <tr>
                            <td>${producto.id}</td>
                            <td>${producto.nombre}</td>
                            <td>
                                <button class="btn btn-sm btn-success" 
                                        onclick="seleccionarProducto(${producto.id}, '${producto.nombre}')">
                                    Seleccionar
                                </button>
                            </td>
                        </tr>
                    `;
                });
            });
    }
    
    function filtrarProductos(searchTerm) {
        const rows = document.getElementById('productosBody').getElementsByTagName('tr');
        searchTerm = searchTerm.toLowerCase();
        
        for (let row of rows) {
            const text = row.textContent.toLowerCase();
            row.style.display = text.includes(searchTerm) ? '' : 'none';
        }
    }
    
    function seleccionarProducto(id, nombre, codigo) {
        Swal.fire({
            title: 'Ingrese la cantidad',
            html: `
                <div class="form-group">
                    <label>Producto: ${nombre}</label><br>
                    <label>Código: ${id}</label><br>
                    <input type="number" id="cantidad" class="form-control" min="1" value="1">
                </div>
            `,
            showCancelButton: true,
            confirmButtonText: 'Agregar',
            cancelButtonText: 'Cancelar',
            preConfirm: () => {
                const cantidad = document.getElementById('cantidad').value;
                if (cantidad < 1) {
                    Swal.showValidationMessage('La cantidad debe ser mayor a 0');
                    return false;
                }
                return {
                    cantidad: cantidad
                };
            }
        }).then((result) => {
            if (result.isConfirmed) {
                document.getElementById('producto_id').value = id;
                document.getElementById('cantidad_producto').value = result.value.cantidad;
                document.querySelector('form').submit();
            }
        });
    }
    </script>
{% endblock %}