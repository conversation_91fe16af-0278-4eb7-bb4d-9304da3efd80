{% extends 'Base/base.html' %}

{% block title %}Reporte de Movimientos - {{ tienda.nombre }}{% endblock %}

{% block content %}
<script>
console.log('🌐 REPORTE WEB - Template cargado');
console.log('🏪 Tienda:', '{{ tienda.nombre }}');
console.log('📅 Fecha:', '{{ fecha|date:"d/m/Y" }}');
console.log('📊 Resumen:', {
    movimientos: {{ resumen.total_movimientos }},
    fel: {{ resumen.total_fel }},
    inventario: {{ resumen.total_inventario }},
    monto: {{ resumen.total_monto }}
});
</script>
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4 no-print">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h1><i class="fas fa-chart-bar"></i> Reporte de Movimientos</h1>
                <div>
                    <form method="POST" style="display: inline;">
                        {% csrf_token %}
                        <input type="hidden" name="tienda_id" value="{{ tienda_id }}">
                        <input type="hidden" name="fecha_reporte" value="{{ fecha_reporte }}">
                        <input type="hidden" name="generar_pdf" value="true">
                        <button type="submit" class="btn btn-success me-2">
                            <i class="fas fa-file-pdf"></i> Generar PDF
                        </button>
                    </form>
                    <button onclick="window.print()" class="btn btn-info me-2">
                        <i class="fas fa-print"></i> Imprimir
                    </button>
                    <a href="{% url 'admin_empleados:reporte_web' %}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Nuevo Reporte
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Título del Reporte -->
    <div class="row mb-4">
        <div class="col-12 text-center">
            <h2 class="mb-1">Movimientos Tienda: {{ tienda.nombre }}</h2>
            <h4 class="text-muted">Fecha: {{ fecha|date:"d/m/Y" }}</h4>
        </div>
    </div>

    <!-- Resumen General -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-chart-pie"></i> RESUMEN GENERAL</h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-md-3">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h3 class="text-primary">{{ resumen.total_movimientos }}</h3>
                                    <p class="mb-0">Total Movimientos</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h3 class="text-success">{{ resumen.total_fel }}</h3>
                                    <p class="mb-0">Ventas FEL</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h3 class="text-warning">{{ resumen.total_inventario }}</h3>
                                    <p class="mb-0">Ajustes Inventario</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h3 class="text-info">Q{{ resumen.total_monto|floatformat:2 }}</h3>
                                    <p class="mb-0">Monto Total</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Productos Vendidos - General -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0"><i class="fas fa-boxes"></i> PRODUCTOS VENDIDOS - GENERAL</h5>
                </div>
                <div class="card-body">
                    {% if productos_vendidos %}
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Producto</th>
                                        <th>Precio Unit.</th>
                                        <th>Cantidad Total</th>
                                        <th>FEL</th>
                                        <th>Inventario</th>
                                        <th>Total Q</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for producto, datos in productos_vendidos.items %}
                                        <tr>
                                            <td><strong>{{ producto }}</strong></td>
                                            <td>Q{{ datos.precio_unitario|floatformat:2 }}</td>
                                            <td>{{ datos.cantidad }} unidades</td>
                                            <td>{{ datos.fel }} unidades</td>
                                            <td>{{ datos.inventario }} unidades</td>
                                            <td>Q{{ datos.total|floatformat:2 }}</td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <p class="text-muted">No hay productos registrados.</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Productos FEL -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0"><i class="fas fa-receipt"></i> VENTAS FEL</h5>
                </div>
                <div class="card-body">
                    {% if productos_fel %}
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Producto</th>
                                        <th>Precio</th>
                                        <th>Cantidad</th>
                                        <th>Total</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for producto, datos in productos_fel.items %}
                                        <tr>
                                            <td>{{ producto }}</td>
                                            <td>Q{{ datos.precio_unitario|floatformat:2 }}</td>
                                            <td>{{ datos.cantidad }}</td>
                                            <td>Q{{ datos.total|floatformat:2 }}</td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                                <tfoot>
                                    <tr class="table-success">
                                        <th>TOTAL FEL:</th>
                                        <th>-</th>
                                        <th>{{ resumen.total_fel }} mov.</th>
                                        <th>Q{{ resumen.total_monto_fel|floatformat:2 }}</th>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    {% else %}
                        <p class="text-muted">No hay ventas FEL registradas.</p>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Productos Inventario -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0"><i class="fas fa-boxes"></i> AJUSTES INVENTARIO</h5>
                </div>
                <div class="card-body">
                    {% if productos_inventario %}
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Producto</th>
                                        <th>Precio</th>
                                        <th>Cantidad</th>
                                        <th>Total</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for producto, datos in productos_inventario.items %}
                                        <tr>
                                            <td>{{ producto }}</td>
                                            <td>Q{{ datos.precio_unitario|floatformat:2 }}</td>
                                            <td>{{ datos.cantidad }}</td>
                                            <td>Q{{ datos.total|floatformat:2 }}</td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                                <tfoot>
                                    <tr class="table-warning">
                                        <th>TOTAL INVENTARIO:</th>
                                        <th>-</th>
                                        <th>{{ resumen.total_inventario }} mov.</th>
                                        <th>Q{{ resumen.total_monto_inventario|floatformat:2 }}</th>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    {% else %}
                        <p class="text-muted">No hay ajustes de inventario registrados.</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Lista de Movimientos -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0"><i class="fas fa-list"></i> DETALLE DE MOVIMIENTOS</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-sm">
                            <thead>
                                <tr>
                                    <th>Recibo</th>
                                    <th>Hora</th>
                                    <th>Tipo</th>
                                    <th>Empleado</th>
                                    <th>Total</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for bitacora in bitacoras %}
                                    <tr>
                                        <td>{{ bitacora.numero_recibo }}</td>
                                        <td>{{ bitacora.fecha|date:"H:i" }}</td>
                                        <td>
                                            {% if bitacora.tipo == 'FEL' %}
                                                <span class="badge bg-success">FEL</span>
                                            {% else %}
                                                <span class="badge bg-warning text-dark">Inventario</span>
                                            {% endif %}
                                        </td>
                                        <td>{{ bitacora.usuario_tienda.nombre_completo }}</td>
                                        <td>Q{{ bitacora.total|floatformat:2 }}</td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
@media print {
    .no-print {
        display: none !important;
    }
    
    .container-fluid {
        margin: 0;
        padding: 0;
    }
    
    .card {
        border: 1px solid #000 !important;
        box-shadow: none !important;
        page-break-inside: avoid;
    }
    
    .card-header {
        background-color: #f8f9fa !important;
        color: #000 !important;
        border-bottom: 1px solid #000 !important;
    }
}
</style>
{% endblock %}
