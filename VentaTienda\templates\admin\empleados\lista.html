{% extends 'Base/base.html' %}
{% load static %}

{% block title %}Gestión de Empleados{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="bx bx-group"></i> Gestión de Empleados
                    </h5>
                    <div>
                        <a href="{% url 'admin_empleados:bitacora_lista' %}" class="btn btn-success me-2">
                            <i class="bx bx-clipboard"></i> Ver Bitácora
                        </a>
                        <a href="{% url 'admin_empleados:crear' %}" class="btn btn-primary">
                            <i class="bx bx-plus"></i> Nuevo Empleado
                        </a>
                    </div>
                </div>
                
                <div class="card-body">
                    <!-- Filtros -->
                    <form method="GET" class="row mb-3">
                        <div class="col-md-4">
                            <input type="text" name="search" class="form-control" 
                                   placeholder="Buscar por usuario, nombre, apellido o email..." 
                                   value="{{ search_query }}">
                        </div>
                        <div class="col-md-3">
                            <select name="tienda" class="form-control">
                                <option value="">Todas las tiendas</option>
                                {% for tienda in tiendas %}
                                    <option value="{{ tienda.id }}" {% if tienda_filter == tienda.id|stringformat:"s" %}selected{% endif %}>
                                        {{ tienda.nombre }}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-2">
                            <button type="submit" class="btn btn-outline-primary">
                                <i class="bx bx-search"></i> Buscar
                            </button>
                        </div>
                        <div class="col-md-2">
                            <a href="{% url 'admin_empleados:lista' %}" class="btn btn-outline-secondary">
                                <i class="bx bx-refresh"></i> Limpiar
                            </a>
                        </div>
                    </form>
                    
                    <!-- Tabla de empleados -->
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Usuario</th>
                                    <th>Nombre Completo</th>
                                    <th>Tienda</th>
                                    <th>Email</th>
                                    <th>Estado</th>
                                    <th>Fecha Creación</th>
                                    <th>Acciones</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for empleado in page_obj %}
                                <tr>
                                    <td>
                                        <strong>{{ empleado.username }}</strong>
                                    </td>
                                    <td>{{ empleado.nombre_completo }}</td>
                                    <td>
                                        <span class="badge bg-info">{{ empleado.tienda.nombre }}</span>
                                    </td>
                                    <td>{{ empleado.email|default:"-" }}</td>
                                    <td>
                                        {% if empleado.activo %}
                                            <span class="badge bg-success">Activo</span>
                                        {% else %}
                                            <span class="badge bg-danger">Inactivo</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ empleado.fecha_creacion|date:"d/m/Y H:i" }}</td>
                                    <td>
                                        <div class="dropdown">
                                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle" 
                                                    type="button" data-bs-toggle="dropdown">
                                                Acciones
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li>
                                                    <a class="dropdown-item" href="{% url 'admin_empleados:detalle' empleado.id %}">
                                                        <i class="bx bx-show"></i> Ver Detalles
                                                    </a>
                                                </li>
                                                <li>
                                                    <a class="dropdown-item" href="{% url 'admin_empleados:editar' empleado.id %}">
                                                        <i class="bx bx-edit"></i> Editar
                                                    </a>
                                                </li>
                                                <li><hr class="dropdown-divider"></li>
                                                <li>
                                                    <a class="dropdown-item" href="{% url 'admin_empleados:toggle_activo' empleado.id %}"
                                                       onclick="return confirm('¿Está seguro de cambiar el estado de este empleado?')">
                                                        {% if empleado.activo %}
                                                            <i class="bx bx-user-x text-danger"></i> Desactivar
                                                        {% else %}
                                                            <i class="bx bx-user-check text-success"></i> Activar
                                                        {% endif %}
                                                    </a>
                                                </li>
                                            </ul>
                                        </div>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="7" class="text-center">
                                        <div class="py-4">
                                            <i class="bx bx-user-x display-4 text-muted"></i>
                                            <p class="text-muted mt-2">No se encontraron empleados</p>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Paginación -->
                    {% if page_obj.has_other_pages %}
                    <nav aria-label="Paginación">
                        <ul class="pagination justify-content-center">
                            {% if page_obj.has_previous %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if tienda_filter %}&tienda={{ tienda_filter }}{% endif %}">
                                        Anterior
                                    </a>
                                </li>
                            {% endif %}
                            
                            {% for num in page_obj.paginator.page_range %}
                                {% if page_obj.number == num %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ num }}</span>
                                    </li>
                                {% else %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ num }}{% if search_query %}&search={{ search_query }}{% endif %}{% if tienda_filter %}&tienda={{ tienda_filter }}{% endif %}">
                                            {{ num }}
                                        </a>
                                    </li>
                                {% endif %}
                            {% endfor %}
                            
                            {% if page_obj.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if tienda_filter %}&tienda={{ tienda_filter }}{% endif %}">
                                        Siguiente
                                    </a>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
