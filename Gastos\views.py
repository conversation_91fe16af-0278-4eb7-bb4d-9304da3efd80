from django.shortcuts import render,redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from user.models import User
from Gastos.models import Gastos
from Gastos.forms import GastosForm,UpdateGastosForm
from Dash.models import Tienda
from django.db.models import Q
from datetime import datetime


@login_required
def nuevo(request):

    form = GastosForm()
    if request.method == 'POST':

        form = GastosForm(request.POST)
        if form.is_valid():
            try:
                p = Gastos()
                p.nombre = form.cleaned_data['nombre']
                p.descripcion = form.cleaned_data['descripcion']
                p.cantidad = form.cleaned_data['cantidad']
                p.precio = form.cleaned_data['precio']
                p.total = form.cleaned_data['cantidad']*form.cleaned_data['precio']
                p.tienda = Tienda.objects.get(id=form.cleaned_data['tienda'].pk)
                p.usuario = User.objects.get(id=request.user.id)
                p.save()
                messages.success(request,f'Gasto {p.nombre} Ingresado Correctamente!')
                return redirect('NuevoGasto')
            except:
                messages.error(request,f'Error Al Ingresar Gasto {p.nombre}!')
                return redirect('NuevoGasto')
        


    return render(request,'Gastos/nuevo.html',{'form':form})
    


@login_required
def listado(request):

    prod = Gastos.objects.all().order_by('id')

    return render(request,'Gastos/listado.html',{'p':prod})


@login_required
def eliminar(request,id):

    prod = Gastos.objects.get(id=id)
    prod.delete()
    messages.success(request, f'Producto {prod.nombre} Eliminado Exitosamente!')
    return redirect('ListaGasto')


@login_required
def actualizar(request,id):
    p = Gastos.objects.get(id=id)
    if request.method == 'GET':
        form = UpdateGastosForm(instance=p)
    else:
        form = UpdateGastosForm(request.POST,instance = p)
     
        if form.is_valid():
            try:
                p.fecha_mod = datetime.today()
                p.usuario = User.objects.get(id=request.user.id)
                form.save()
                messages.success(request, f'Gasto {p.nombre} Modificado Exitosamente!')
                return redirect('ListaGasto')
            except:
                messages.error(request, f'No Se Pudo Modificar Gasto {p.nombre}!')
                return redirect('ListaGasto')

    return render(request,'Gastos/actualizar.html',{'form':form})