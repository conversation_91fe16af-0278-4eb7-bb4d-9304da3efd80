from django import forms
from django.forms import ModelForm
from Productos.models import Producto

ESTADO = (
(0,'Baja'),
(1,'Activo'),
)
UNIDAD = (
('Saco','Saco'),
('Libra','Libra'),
('<PERSON><PERSON>','<PERSON><PERSON>'),
('<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>'),
('Far<PERSON>','Far<PERSON>'),
('Tira','Tira'),
('<PERSON>alo<PERSON>','Galo<PERSON>'),
('Unidades','Unidades'),
('<PERSON>quet<PERSON>','Paquete'),
)
#forms.Select(attrs={'class': 'selectpicker form-control','data-style':'btn-outline-info','placeholder':'Tipo','require':True},choices=TIPO),
class ProductoForm(ModelForm):

    class Meta:
        model = Producto
        fields = ['nombre','medida','inicio','salida','ingreso','final','tienda']
        
        labels = {'nombre':'Nombre de Producto','medida':'Unidad de Medida','inicio':'Inicio','ingreso':'Ingreso','final':'Total','tienda':'Tienda'}     

        widgets = { 

                 'nombre':forms.TextInput(attrs={'class': 'form-control','placeholder':'Nombre de Bebida','autofocus': True,'require':True}),
                 'medida':forms.Select(attrs={'class': 'selectpicker form-control','data-style':'btn-outline-info','placeholder':'Tipo','require':True},choices=UNIDAD),
                 'inicio':forms.TextInput(attrs={'class': 'form-control','id':'inicio','placeholder':'0','type': 'text','require':False}),
                 'ingreso':forms.TextInput(attrs={'class': 'form-control','id':'ingreso','placeholder':'0','type': 'text','require':False}),
                 'salida':forms.TextInput(attrs={'class': 'form-control','id':'salida','placeholder':'0','type': 'text','require':False}),
                 'final':forms.TextInput(attrs={'class': 'form-control','id':'final','placeholder':'0','type': 'number','require':False,'readonly':True}),
                 'tienda':forms.Select(attrs={'class': 'selectpicker form-control','data-style':'btn-outline-info','placeholder':'Tienda','require':True}),
        }         


class UpdateProductoForm(ModelForm):

    class Meta:
        model = Producto
        fields = ['nombre','medida','inicio','salida','ingreso','final','tienda']
        
        labels = {'nombre':'Nombre de Producto','medida':'Unidad de Medida','inicio':'Inicio','ingreso':'Ingreso','final':'Total','tienda':'Tienda'}     

        widgets = { 

                 'nombre':forms.TextInput(attrs={'class': 'form-control','placeholder':'Nombre de Bebida','autofocus': True,'require':True}),
                 'medida':forms.Select(attrs={'class': 'selectpicker form-control','data-style':'btn-outline-info','placeholder':'Tipo','require':True},choices=UNIDAD),
                 'inicio':forms.TextInput(attrs={'class': 'form-control','id':'inicio','placeholder':'Porcio de Bebida','type': 'text','require':False,'readonly':True}),
                 'ingreso':forms.TextInput(attrs={'class': 'form-control','id':'ingreso','placeholder':'Porcio de Bebida','type': 'text','require':False}),
                 'salida':forms.TextInput(attrs={'class': 'form-control','id':'salida','placeholder':'Porcio de Bebida','type': 'text','require':False}),
                 'final':forms.TextInput(attrs={'class': 'form-control','id':'final','placeholder':'0','type': 'number','require':False,'readonly':True}),
                 'tienda':forms.Select(attrs={'class': 'selectpicker form-control','data-style':'btn-outline-info','placeholder':'Tienda','require':True}),
                 'estado':forms.Select(attrs={'class': 'selectpicker form-control','data-style':'btn-outline-info','placeholder':'Tipo','require':True},choices=ESTADO),
        }      
