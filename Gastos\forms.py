from django import forms
from django.forms import ModelForm
from Gastos.models import Gastos

ESTADO = (
(0,'Baja'),
(1,'Activo'),
)

#forms.Select(attrs={'class': 'selectpicker form-control','data-style':'btn-outline-info','placeholder':'Tipo','require':True},choices=TIPO),
class GastosForm(ModelForm):

    class Meta:
        model = Gastos
        fields = ['nombre','descripcion','factura','cantidad','precio','tienda']
        
        labels = {'nombre':'Nombre de Gasto','descripcion':'Descripcion','factura':'Factura','cantidad':'Cantidad','precio':'Precio','tienda':'Tienda'}     

        widgets = { 

                 'nombre':forms.TextInput(attrs={'class': 'form-control','placeholder':'Nombre de Gasto','autofocus': True,'require':True}),
                 'descripcion':forms.TextInput(attrs={'class': 'form-control','placeholder':'Descripcion','autofocus': True,'require':True}),
                 'factura':forms.TextInput(attrs={'class': 'form-control','placeholder':'Descripcion','autofocus': True,'require':True}),
                 'cantidad':forms.TextInput(attrs={'type':'number','class': 'form-control','placeholder':'cantidad','type': 'text','require':False}),
                 'precio':forms.TextInput(attrs={'type':'number','class': 'form-control','placeholder':'Porcio de Bebida','type': 'text','require':False}),
                 'tienda':forms.Select(attrs={'class': 'selectpicker form-control','data-style':'btn-outline-info','placeholder':'Tienda','require':True}),
        }         


class UpdateGastosForm(ModelForm):

    class Meta:    
        model = Gastos
        fields = ['nombre','descripcion','factura','cantidad','precio','tienda','estado']
        
        labels = {'nombre':'Nombre de Gasto','descripcion':'Descripcion','factura':'Factura','cantidad':'Cantidad','precio':'Precio','tienda':'Tienda','estado':'Estado'}     

        widgets = { 

                 'nombre':forms.TextInput(attrs={'class': 'form-control','placeholder':'Nombre de Gasto','autofocus': True,'require':True}),
                 'descripcion':forms.TextInput(attrs={'class': 'form-control','placeholder':'Descripcion','autofocus': True,'require':True}),
                 'factura':forms.TextInput(attrs={'class': 'form-control','placeholder':'Descripcion','autofocus': True,'require':True}),
                 'cantidad':forms.TextInput(attrs={'type':'number','class': 'form-control','placeholder':'cantidad','type': 'text','require':False}),
                 'precio':forms.TextInput(attrs={'type':'number','class': 'form-control','placeholder':'Porcio de Bebida','type': 'text','require':False}),
                 'tienda':forms.Select(attrs={'class': 'selectpicker form-control','data-style':'btn-outline-info','placeholder':'Tienda','require':True}),
                 'estado':forms.Select(attrs={'class': 'selectpicker form-control','data-style':'btn-outline-info','placeholder':'Tipo','require':True},choices=ESTADO),
        }      
