from django.shortcuts import render,redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from user.models import User
from Productos.models import Producto
from Productos.forms import ProductoForm,UpdateProductoForm
from Dash.models import Tienda
from django.db.models import Q
from datetime import datetime
from Venta.models import Venta


@login_required
def nuevo(request):

    form = ProductoForm()
    if request.method == 'POST':

        form = ProductoForm(request.POST)
        if form.is_valid():
            try:
                p = Producto()
                p.nombre = form.cleaned_data['nombre']
                p.medida = form.cleaned_data['medida']
                p.inicio = form.cleaned_data['inicio']
                p.ingreso = form.cleaned_data['ingreso']
                p.salida = form.cleaned_data['salida']
                p.final = form.cleaned_data['final']
                p.precio_compra = form.cleaned_data['precio_compra']
                p.precio_venta = form.cleaned_data['precio_venta']
                p.total_venta = form.cleaned_data['precio_venta'] * form.cleaned_data['salida']
                p.tienda = Tienda.objects.get(id=form.cleaned_data['tienda'].pk)
                p.usuario = User.objects.get(id=request.user.id)
                p.save()
                messages.success(request,f'Producto {p.nombre} Ingresado Correctamente!')
                return redirect('NuevoProducto')
            except:
                messages.error(request,f'Error Al Ingresar Producto {p.nombre}!')
                return redirect('NuevoProducto')
        


    return render(request,'Productos/nuevo.html',{'form':form})


def nueva_venta(pr,c,pre,t,td,u):
        print(pr,c,pre,t,td,u)
        p = Producto.objects.get(id=pr)
        try:
                
                if p.final >= int(c):
                    v = Venta()
                    v.prod = Producto.objects.get(id=pr)
                    v.cantidad = c
                    v.precio = pre
                    v.total = t
                    v.tienda = Tienda.objects.get(id=td)
                    v.usuario = User.objects.get(id=u)
                    v.save()
                    Producto.objects.filter(id=v.prod.pk).update(final=p.final-v.cantidad,inicio=p.final-v.cantidad)
                else:
                    pass
        except:
            pass
            


from django.shortcuts import render
from .models import Producto
from Dash.models import Tienda


@login_required
def listado(request):
    tienda_id = request.GET.get('tienda')  # Obtener la tienda seleccionada
    tiendas = Tienda.objects.all()  # Lista de todas las tiendas
    productos = Producto.objects.all().order_by('id')  # Mostrar todos por defecto

    if tienda_id:
        productos = productos.filter(tienda_id=tienda_id)  # Filtrar productos por tienda seleccionada

    context = {
        'p': productos,
        'tiendas': tiendas,
        'tienda_seleccionada': tienda_id,  # Pasar la tienda seleccionada para mantenerla en el formulario
    }
    return render(request, 'Productos/listado.html', context)


@login_required
def eliminar(request,id):

    prod = Producto.objects.get(id=id)
    prod.delete()
    messages.success(request, f'Producto {prod.nombre} Eliminado Exitosamente!')
    return redirect('ListaProducto')


@login_required
def actualizar(request, id):
    p = Producto.objects.get(id=id)
    if request.method == 'GET':
        form = UpdateProductoForm(instance=p)
    else:
        form = UpdateProductoForm(request.POST, instance=p)
        
        if form.is_valid():
            try:
                p.fecha_mod = datetime.today()
                p.usuario = User.objects.get(id=request.user.id)
                p.inicio = p.final
                p.precio_compra = form.cleaned_data['precio_compra']
                p.precio_venta = form.cleaned_data['precio_venta']
                form.save()
                
                # Obtener el ID de la tienda del producto
                tienda_id = p.tienda.id
                #nueva_venta(p,c,pr,t,td,u)
                nueva_venta(p.id,form.cleaned_data['salida'],form.cleaned_data['precio_venta'],form.cleaned_data['total_venta'],p.tienda.id,p.usuario.id)
                messages.success(request, f'Producto {p.nombre} Modificado Exitosamente!')
                
                # Redirigir a productos por tienda, pasando el ID de la tienda
                return redirect('productos_por_tienda', tienda=tienda_id)
            except:
                messages.error(request, f'No Se Pudo Modificar Producto {p.nombre}!')
                return redirect('productos_por_tienda')

    return render(request, 'Productos/actualizar.html', {'form': form})



from django.shortcuts import render
from .models import Producto
from Dash.models import Tienda

def productos_por_tienda(request, tienda):
    # Filtrar productos por la tienda seleccionada
    if tienda:
        productos = Producto.objects.filter(tienda__id=tienda)
        tienda_obj = Tienda.objects.get(id=tienda)
    else:
        productos = Producto.objects.all()  # Mostrar todos los productos si no se selecciona tienda
        tienda_obj = None

    # Obtener todas las tiendas para el filtro
    tiendas = Tienda.objects.all()
    
    context = {
        'productos': productos,
        'tiendas': tiendas,
        'tienda_seleccionada': tienda,
        'tienda': tienda_obj,
    }
    return render(request, 'Productos/listado-productos.html', context)




