{% extends 'Base/base.html' %}

{% block title %}Detalle Bitácora #{{ bitacora.id }}{% endblock %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h1><i class="fas fa-clipboard-list"></i> Detalle Bitácora #{{ bitacora.id }}</h1>
                <div>
                    {% if bitacora.tipo == 'FEL' %}
                        <a href="{% url 'venta_tienda:pdf_recibo' bitacora.numero_recibo %}" 
                           class="btn btn-success me-2" target="_blank">
                            <i class="fas fa-file-pdf"></i> Ver PDF
                        </a>
                    {% endif %}
                    <a href="{% url 'admin_empleados:bitacora_lista' %}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Volver a Lista
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Información General -->
        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-info-circle"></i> Información General</h5>
                </div>
                <div class="card-body">
                    <table class="table table-borderless">
                        <tr>
                            <td><strong>ID:</strong></td>
                            <td>{{ bitacora.id }}</td>
                        </tr>
                        <tr>
                            <td><strong>Número de Recibo:</strong></td>
                            <td>{{ bitacora.numero_recibo }}</td>
                        </tr>
                        <tr>
                            <td><strong>Fecha:</strong></td>
                            <td>{{ bitacora.fecha|date:"d/m/Y H:i:s" }}</td>
                        </tr>
                        <tr>
                            <td><strong>Tipo:</strong></td>
                            <td>
                                {% if bitacora.tipo == 'FEL' %}
                                    <span class="badge bg-success">FEL - Venta con Factura</span>
                                {% else %}
                                    <span class="badge bg-warning">Inventario - Registro sin Factura</span>
                                {% endif %}
                            </td>
                        </tr>
                        <tr>
                            <td><strong>Tienda:</strong></td>
                            <td>{{ bitacora.tienda.nombre }}</td>
                        </tr>
                        <tr>
                            <td><strong>Empleado:</strong></td>
                            <td>{{ bitacora.usuario_tienda.nombre_completo }}</td>
                        </tr>
                        <tr>
                            <td><strong>Total:</strong></td>
                            <td><span class="h5 text-success">Q{{ bitacora.total|floatformat:2 }}</span></td>
                        </tr>
                        <tr>
                            <td><strong>Estado:</strong></td>
                            <td>
                                {% if bitacora.estado == 1 %}
                                    <span class="badge bg-success">Activo</span>
                                {% else %}
                                    <span class="badge bg-danger">Anulado</span>
                                {% endif %}
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>

        <!-- Información del Cliente (solo para FEL) -->
        {% if bitacora.tipo == 'FEL' %}
        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-user"></i> Información del Cliente</h5>
                </div>
                <div class="card-body">
                    <table class="table table-borderless">
                        <tr>
                            <td><strong>NIT:</strong></td>
                            <td>{{ bitacora.cliente_nit|default:"C/F" }}</td>
                        </tr>
                        <tr>
                            <td><strong>Nombre:</strong></td>
                            <td>{{ bitacora.cliente_nombre|default:"Consumidor Final" }}</td>
                        </tr>
                        <tr>
                            <td><strong>Dirección:</strong></td>
                            <td>{{ bitacora.cliente_direccion|default:"Ciudad" }}</td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Observaciones -->
        {% if bitacora.observaciones %}
        <div class="col-12">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-comment"></i> Observaciones</h5>
                </div>
                <div class="card-body">
                    <p class="mb-0">{{ bitacora.observaciones }}</p>
                </div>
            </div>
        </div>
        {% endif %}
    </div>

    <!-- Detalles de Productos/Recetas -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-shopping-cart"></i> Productos y Recetas</h5>
                </div>
                <div class="card-body">
                    {% if detalles %}
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead class="table-dark">
                                    <tr>
                                        <th>Tipo</th>
                                        <th>Nombre</th>
                                        <th>Precio Unitario</th>
                                        <th>Cantidad</th>
                                        <th>Subtotal</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for detalle in detalles %}
                                        <tr>
                                            <td>
                                                {% if detalle.producto %}
                                                    <span class="badge bg-primary">
                                                        <i class="fas fa-box"></i> Producto
                                                    </span>
                                                {% else %}
                                                    <span class="badge bg-success">
                                                        <i class="fas fa-utensils"></i> Receta
                                                    </span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                <strong>{{ detalle.nombre_item }}</strong>
                                                {% if detalle.producto and detalle.producto.descripcion %}
                                                    <br><small class="text-muted">{{ detalle.producto.descripcion }}</small>
                                                {% elif detalle.receta and detalle.receta.descripcion %}
                                                    <br><small class="text-muted">{{ detalle.receta.descripcion }}</small>
                                                {% endif %}
                                            </td>
                                            <td>Q{{ detalle.precio_unitario|floatformat:2 }}</td>
                                            <td>{{ detalle.cantidad }}</td>
                                            <td><strong>Q{{ detalle.subtotal|floatformat:2 }}</strong></td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                                <tfoot class="table-light">
                                    <tr>
                                        <th colspan="4" class="text-end">TOTAL:</th>
                                        <th>Q{{ bitacora.total|floatformat:2 }}</th>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No hay detalles registrados</h5>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
