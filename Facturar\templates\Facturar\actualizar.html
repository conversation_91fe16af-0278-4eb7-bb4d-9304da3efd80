{% extends 'Base/base.html' %}
{% load static %}


{% block content %}

{% if messages %}
{% for message in messages %}
<script>
  Swal.fire({
    "title": "Informacion Sistema",
    "text": "{{message}}",
    "icon": "{{message.tags}}"
  })
</script>
{% endfor %}
{% endif %}


<div class="container">
  <h3>FORMULARIO DE ACTUALIZACION FACTURACION</h3>
  <form action="#" method="POST" enctype="multipart/form-data">{% csrf_token %}
  <div class="row">

    
    <div class="col-md-3">
      <label>{{form.facturar.label_tag}}</label>
      {{form.facturar}}
    </div>
    <div class="col-md-3">
      <label>{{form.fecha.label_tag}}</label>
      {{form.fecha}}
    </div>

  </div><br>


  <div class="row">

    <div class="col-md-4">
      <button type="submit" class="btn btn-success">Actualizar</button>
      <a href="{% url 'Admin' %}" class="btn btn-danger">Cancelar</a>
    </div>

  </div><br>
  </form>
</div>



<script>
  let precio1 = document.getElementById("ventas")
  let precio2 = document.getElementById("gastos")
  let precio3 = document.getElementById("liquido")
  
  precio2.addEventListener("change", () => {
      precio3.value = (parseFloat(precio1.value) - parseFloat(precio2.value))

  })
  
</script>


{% endblock %}