{% extends 'Base/base.html' %}
{% load static %}

{% block content %}
<div class="container py-5">
    <div class="card shadow">
        <div class="card-header bg-primary text-white">
            <h3 class="mb-0">FORMULARIO DE PRODUCTOS</h3>
        </div>
        <br>
        <div class="card-body">
            <form id="productoForm" method="POST" enctype="multipart/form-data">
                {% csrf_token %}
                <div class="row g-3">
                    <div class="col-md-3">
                        <div class="form-floating">
                            {{ form.nombre }}
                            <label>{{ form.nombre.label }}</label>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-floating">
                            {{ form.medida }}
                            <label>{{ form.medida.label }}</label>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-floating">
                            {{ form.precio_compra }}
                            <label>{{ form.precio_compra.label }}</label>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-floating">
                            {{ form.precio_venta }}
                            <label>{{ form.precio_venta.label }}</label>
                        </div>
                    </div>
                </div>
                <div class="row g-3 mt-3">
                    <div class="col-md-3">
                        <div class="form-floating">
                            {{ form.tienda }}
                            <label>{{ form.tienda.label }}</label>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-floating">
                            {{ form.id_cate }}
                            <label>{{ form.id_cate.label }}</label>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <label for="{{ form.imagen.id_for_label }}" class="form-label">{{ form.imagen.label }}</label>
                        {{ form.imagen }}
                    </div>
                </div>
                <div class="row g-3 mt-3">
                    <div class="col-md-3">
                        <div class="form-floating">
                            <input type="text" class="form-control" value="{% now 'd-m-Y' %}" readonly>
                            <label>Fecha Ingreso</label>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-floating">
                            <input type="text" class="form-control" value="{{ user.username }}" readonly>
                            <label>Usuario</label>
                        </div>
                    </div>
                </div>
                <div class="mt-4">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>Guardar
                    </button>
                    <a href="{% url 'ListaProducto' %}" class="btn btn-danger">
                        <i class="fas fa-times me-2"></i>Cancelar
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}