# Generated by Django 4.2.16 on 2025-06-10 03:35

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('Receta', '0001_initial'),
        ('Productos', '0010_remove_producto_ingreso'),
        ('Venta', '0002_alter_venta_fecha'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='venta',
            options={},
        ),
        migrations.RemoveField(
            model_name='venta',
            name='cantidad',
        ),
        migrations.RemoveField(
            model_name='venta',
            name='fecha_mod',
        ),
        migrations.RemoveField(
            model_name='venta',
            name='precio',
        ),
        migrations.RemoveField(
            model_name='venta',
            name='prod',
        ),
        migrations.AddField(
            model_name='venta',
            name='observaciones',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='venta',
            name='estado',
            field=models.BooleanField(default=True),
        ),
        migrations.AlterField(
            model_name='venta',
            name='fecha',
            field=models.DateTimeField(auto_now_add=True),
        ),
        migrations.CreateModel(
            name='DetalleVenta',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('cantidad', models.IntegerField()),
                ('precio_unitario', models.DecimalField(decimal_places=2, max_digits=12)),
                ('subtotal', models.DecimalField(decimal_places=2, max_digits=12)),
                ('producto', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='Productos.producto')),
                ('receta', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='Receta.receta')),
                ('venta', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='detalles', to='Venta.venta')),
            ],
        ),
    ]
