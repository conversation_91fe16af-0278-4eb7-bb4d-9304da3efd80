{% extends 'Base/base.html' %}
{% load static %}

{% block title %}Detalle del Empleado{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="bx bx-user"></i> Detalle del Empleado
                    </h5>
                    <div>
                        <a href="{% url 'admin_empleados:editar' empleado.id %}" class="btn btn-primary btn-sm">
                            <i class="bx bx-edit"></i> Editar
                        </a>
                        <a href="{% url 'admin_empleados:lista' %}" class="btn btn-secondary btn-sm">
                            <i class="bx bx-arrow-back"></i> Volver
                        </a>
                    </div>
                </div>
                
                <div class="card-body">
                    <div class="row">
                        <!-- Información Personal -->
                        <div class="col-md-6">
                            <h6 class="text-muted mb-3">
                                <i class="bx bx-id-card"></i> Información Personal
                            </h6>
                            
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Usuario:</strong></td>
                                    <td>{{ empleado.username }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Nombre Completo:</strong></td>
                                    <td>{{ empleado.nombre_completo }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Email:</strong></td>
                                    <td>{{ empleado.email|default:"-" }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Teléfono:</strong></td>
                                    <td>{{ empleado.telefono|default:"-" }}</td>
                                </tr>
                                <tr>
                                    <td><strong>DPI:</strong></td>
                                    <td>{{ empleado.dpi }}</td>
                                </tr>
                            </table>
                        </div>
                        
                        <!-- Información de Trabajo -->
                        <div class="col-md-6">
                            <h6 class="text-muted mb-3">
                                <i class="bx bx-briefcase"></i> Información de Trabajo
                            </h6>
                            
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Tienda:</strong></td>
                                    <td>
                                        <span class="badge bg-info">{{ empleado.tienda.nombre }}</span>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Estado:</strong></td>
                                    <td>
                                        {% if empleado.activo %}
                                            <span class="badge bg-success">Activo</span>
                                        {% else %}
                                            <span class="badge bg-danger">Inactivo</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Fecha de Creación:</strong></td>
                                    <td>{{ empleado.fecha_creacion|date:"d/m/Y H:i" }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Total de Ventas:</strong></td>
                                    <td>
                                        <span class="badge bg-primary">{{ total_ventas }}</span>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Ventas Recientes -->
    {% if ventas_recientes %}
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="bx bx-receipt"></i> Ventas Recientes
                    </h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Recibo</th>
                                    <th>Fecha</th>
                                    <th>Total</th>
                                    <th>Estado</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for venta in ventas_recientes %}
                                <tr>
                                    <td>{{ venta.numero_recibo }}</td>
                                    <td>{{ venta.fecha|date:"d/m/Y H:i" }}</td>
                                    <td>Q{{ venta.total|floatformat:2 }}</td>
                                    <td>
                                        {% if venta.estado == 1 %}
                                            <span class="badge bg-success">Activo</span>
                                        {% else %}
                                            <span class="badge bg-danger">Anulado</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}
