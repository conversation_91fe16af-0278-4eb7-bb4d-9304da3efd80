# Generated by Django 4.2.16 on 2025-05-17 22:40

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('Dash', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('Productos', '0005_rename_fecha_creacion_producto_fecha_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='Receta',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('nombre', models.CharField(max_length=250)),
                ('descripcion', models.CharField(max_length=850)),
                ('unidad', models.CharField(default='S/U', max_length=850)),
                ('cantidad', models.IntegerField(default=0)),
                ('tiempo', models.Char<PERSON>ield(blank=True, default='0', max_length=850, null=True)),
                ('precio_receta', models.DecimalField(decimal_places=2, default=0.0, max_digits=12)),
                ('estado', models.IntegerField(default=1)),
                ('fecha', models.DateField()),
                ('token', models.UUIDField(default=uuid.uuid4, editable=False)),
                ('tienda', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='Dash.tienda')),
                ('usuario', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['nombre'],
            },
        ),
        migrations.CreateModel(
            name='SolicitarReceta',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('cantidad', models.IntegerField()),
                ('tiempo', models.IntegerField(default=0)),
                ('precio_receta', models.DecimalField(decimal_places=2, default=0.0, max_digits=12)),
                ('estado', models.IntegerField(default=0)),
                ('fecha', models.DateField()),
                ('fecha_entrega', models.DateField()),
                ('token', models.UUIDField(default=uuid.uuid4, editable=False)),
                ('id_receta', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='Receta.receta')),
                ('usuario', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['id_receta'],
            },
        ),
        migrations.CreateModel(
            name='DetalleReceta',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('cantidad', models.IntegerField()),
                ('precio_materia', models.DecimalField(decimal_places=2, default=0.0, max_digits=12)),
                ('total', models.DecimalField(decimal_places=2, default=0.0, max_digits=12)),
                ('estado', models.IntegerField(default=1)),
                ('fecha', models.DateField()),
                ('token', models.UUIDField(default=uuid.uuid4, editable=False)),
                ('id_receta', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='Receta.receta')),
                ('producto', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='Productos.producto')),
                ('usuario', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['id_receta'],
            },
        ),
    ]
