{% extends 'Base/base.html' %}
{% load static %}

{% block content %}
{% if messages %}
    {% for message in messages %}
    <script>
        Swal.fire({
            title: "Información Sistema",
            text: "{{ message }}",
            icon: "{{ message.tags }}"
        })
    </script>
    {% endfor %}
{% endif %}

<div class="container py-4">
    <div class="card shadow">
        <!-- ──────────────────────  ENCABEZADO  ────────────────────── -->
        <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
            <h3 class="mb-0">LISTADO DE VENTAS</h3>
            <a href="{% url 'NuevaVenta' %}" class="btn btn-light">
                <i class="fas fa-plus"></i> Nueva Venta
            </a>
        </div>
<br>
        <div class="card-body">
            <!-- ──────────────────────  FILTROS  ────────────────────── -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="form-floating">
                        <input type="date" class="form-control" id="fecha_inicio" name="fecha_inicio"
                               value="{{ fecha_inicio|default:'' }}">
                        <label>Fecha Inicio</label>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-floating">
                        <input type="date" class="form-control" id="fecha_fin" name="fecha_fin"
                               value="{{ fecha_fin|default:'' }}">
                        <label>Fecha Fin</label>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-floating">
                        <select class="form-select" id="tienda_filter" name="tienda">
                            <option value="">Todas las tiendas</option>
                            {% for tienda in tiendas %}
                                <option value="{{ tienda.id }}"
                                    {% if tienda.id|stringformat:"s" == tienda_seleccionada %}selected{% endif %}>
                                    {{ tienda.nombre }}
                                </option>
                            {% endfor %}
                        </select>
                        <label>Tienda</label>
                    </div>
                </div>
                <div class="col-md-2">
                    <button type="button" class="btn btn-primary w-100" id="aplicarFiltros">
                        <i class="fas fa-filter"></i> Filtrar
                    </button>
                </div>
            </div>

            <!-- ──────────────────────  BUSCADOR  ────────────────────── -->
            <div class="row mb-3">
                <div class="col-md-4">
                    <div class="form-floating">
                        <input type="text" class="form-control light-table-filter" data-table="order-table"
                               placeholder="Buscar.." id="buscador">
                        <label>Buscar en la tabla</label>
                    </div>
                </div>
            </div>

            <!-- ──────────────────────  TABLA  ────────────────────── -->
            <div class="table-responsive">
                <table class="table table-hover order-table">
                    <thead class="table-light">
                        <tr>
                            <th>#</th>
                            <th>Fecha</th>
                            <th>Tienda</th>
                            <th># Ítems</th>
                            <th>Total</th>
                            <th>Usuario</th>
                            <th>Acciones</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for venta in ventas %}
                            <tr>
                                <td>{{ venta.id }}</td>
                                <td>{{ venta.fecha|date:"d/m/Y H:i" }}</td>
                                <td>{{ venta.tienda.nombre }}</td>
                                <td>{{ venta.detalles.count }}</td>
                                <td>Q.{{ venta.total|floatformat:2 }}</td>
                                <td>{{ venta.usuario.username }}</td>
                                <td>
                                    <div class="btn-group">
                                        <button type="button" class="btn btn-info btn-sm"
                                                onclick="verDetalleVenta({{ venta.id }})">
                                            <i class="fas fa-eye">Ver</i>
                                        </button>
                                        {% if venta.estado %}
                                            <a href="#" class="btn btn-warning btn-sm" title="Editar (no implementado)">
                                                <i class="fas fa-edit">Editar</i>
                                            </a>
                                            <button type="button" class="btn btn-danger btn-sm"
                                                    onclick="confirmarEliminar({{ venta.id }})">
                                                <i class="fas fa-trash">Eliminar</i>
                                            </button>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                        {% empty %}
                            <tr>
                                <td colspan="7" class="text-center">No hay ventas registradas</td>
                            </tr>
                        {% endfor %}
                    </tbody>
                    <tfoot class="table-primary">
                        <tr>
                            <td colspan="4" class="text-end"><strong>Total General:</strong></td>
                            <td><strong>Q.{{ total_general|floatformat:2 }}</strong></td>
                            <td colspan="2"></td>
                        </tr>
                    </tfoot>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- ──────────────────────  SCRIPTS  ────────────────────── -->
<script>
/* -----------------------  buscador ---------------------- */
(function (document) {
    'use strict';
    const LightTableFilter = (Arr => {
        let _input;
        const _onInputEvent = e => {
            _input = e.target;
            const tables = document.getElementsByClassName(_input.dataset.table);
            Arr.forEach.call(tables, table => {
                Arr.forEach.call(table.tBodies, tbody => {
                    Arr.forEach.call(tbody.rows, _filter);
                });
            });
        };
        const _filter = row => {
            const text = row.textContent.toLowerCase();
            const val = _input.value.toLowerCase();
            row.style.display = text.indexOf(val) === -1 ? 'none' : 'table-row';
        };
        return {
            init() {
                const inputs = document.getElementsByClassName('light-table-filter');
                Arr.forEach.call(inputs, input => {
                    input.oninput = _onInputEvent;
                });
            }
        };
    })(Array.prototype);
    document.addEventListener('readystatechange', () => {
        if (document.readyState === 'complete') {
            LightTableFilter.init();
        }
    });
})(document);

/* ------------------------  filtros ---------------------- */
document.getElementById('aplicarFiltros').addEventListener('click', () => {
    const fechaInicio = document.getElementById('fecha_inicio').value;
    const fechaFin = document.getElementById('fecha_fin').value;
    const tienda    = document.getElementById('tienda_filter').value;

    let url = window.location.pathname + '?';
    if (fechaInicio) url += `fecha_inicio=${fechaInicio}&`;
    if (fechaFin)    url += `fecha_fin=${fechaFin}&`;
    if (tienda)      url += `tienda=${tienda}`;
    window.location.href = url.replace(/&$/, '');
});

/* --------------------  eliminar venta ------------------- */
function confirmarEliminar(ventaId) {
    Swal.fire({
        title: '¿Estás seguro?',
        text: "Esta acción no se puede deshacer",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        confirmButtonText: 'Sí, eliminar',
        cancelButtonText: 'Cancelar'
    }).then(result => {
        if (result.isConfirmed) {
            window.location.href = `/venta/eliminar/${ventaId}/`;
        }
    });
}

/* --------------------  detalle venta -------------------- */
function verDetalleVenta(ventaId) {
    fetch(`/venta/detalle-venta/${ventaId}/`)
        .then(resp => resp.text())
        .then(html => {
            Swal.fire({
                title: `Detalle de Venta #${ventaId}`,
                html: html,
                width: '70%',           //  ---->  mínimo 80 % pantalla
                showCloseButton: true,
                customClass: { popup: 'swal2-border-radius' }
            });
        })
        .catch(() => {
            Swal.fire('Error', 'No se pudo cargar el detalle de la venta', 'error');
        });
}
</script>
{% endblock %}