import datetime
from django.shortcuts import render, redirect, get_object_or_404
from django.http import JsonResponse
from django.views.decorators.http import require_POST, require_GET
from django.db.models import Q
from django.contrib import messages
import json
from django.db.models import Sum
from datetime import datetime, timedelta, timezone

from Categoria.models import Categoria
from Productos.models import Producto
from Receta.models import Receta
from .models import UsuarioTienda, BitacoraVenta, DetalleBitacora, CajaTienda
from .forms import BitacoraVentaForm, CajaTiendaForm
from .permissions import tienda_login_required, get_current_usuario_tienda, get_current_tienda

import emisor
import receptor
import InfileFel
from django.http import HttpResponse


@tienda_login_required
def menu_principal(request):
    """Vista para el menú principal con las 3 cards"""
    usuario_tienda = get_current_usuario_tienda(request)
    tienda = get_current_tienda(request)

    context = {
        'usuario_tienda': usuario_tienda,
        'tienda': tienda,
    }
    return render(request, 'venta_tienda/menu_principal.html', context)

@tienda_login_required
def venta_view(request):
    """Vista principal para la interfaz de venta"""
    usuario_tienda = get_current_usuario_tienda(request)
    tienda = get_current_tienda(request)

    # Obtener todas las categorías
    categorias = Categoria.objects.all()

    # Formulario para observaciones
    form = BitacoraVentaForm()

    context = {
        'categorias': categorias,
        'tienda': tienda,
        'usuario_tienda': usuario_tienda,
        'form': form,
    }

    return render(request, 'venta_tienda/venta.html', context)

@tienda_login_required
def get_productos_categoria(request, categoria_id):
    """API para obtener productos y recetas por categoría"""
    tienda = get_current_tienda(request)

    # Obtener productos de la categoría
    productos = Producto.objects.filter(
        id_cate_id=categoria_id,
        estado=True,
        tienda=tienda
    )

    # Obtener recetas de la categoría (asumiendo que tienen relación con categoría)
    # Nota: Necesitaremos agregar campo categoria a Receta más adelante
    recetas = Receta.objects.filter(
        estado=1,
        tienda=tienda
    )

    items_list = []

    # Agregar productos
    for producto in productos:
        imagen_url = producto.imagen.url if producto.imagen else None
        items_list.append({
            'id': f'producto_{producto.id}',
            'nombre': producto.nombre,
            'precio': float(producto.precio_venta),
            'medida': producto.medida,
            'tipo': 'producto',
            'item_id': producto.id,
            'imagen': imagen_url
        })

    # Agregar recetas
    for receta in recetas:
        imagen_url = receta.imagen.url if receta.imagen else None
        items_list.append({
            'id': f'receta_{receta.id}',
            'nombre': receta.nombre,
            'precio': float(receta.precio_receta),
            'medida': receta.unidad,
            'tipo': 'receta',
            'item_id': receta.id,
            'imagen': imagen_url
        })

    return JsonResponse({'items': items_list})



@tienda_login_required
def caja_view(request):
    """Vista para gestión de caja diaria"""
    usuario_tienda = get_current_usuario_tienda(request)
    tienda = get_current_tienda(request)

    from datetime import date
    hoy = date.today()

    # Verificar si ya existe registro de caja para hoy
    caja_hoy = CajaTienda.objects.filter(tienda=tienda, fecha=hoy).first()

    if request.method == 'POST':
        # Solo permitir registro si no existe caja para hoy
        if caja_hoy:
            messages.error(request, 'La caja del día ya ha sido registrada y no se puede modificar.')
            return redirect('venta_tienda:caja')

        form = CajaTiendaForm(request.POST)

        if form.is_valid():
            caja = form.save(commit=False)
            caja.tienda = tienda
            caja.usuario_tienda = usuario_tienda
            caja.fecha = hoy
            caja.save()

            messages.success(request, 'Caja registrada correctamente')
            return redirect('venta_tienda:caja')
    else:
        # Solo mostrar formulario si no existe caja para hoy
        if not caja_hoy:
            form = CajaTiendaForm()
        else:
            form = None

    context = {
        'form': form,
        'caja_hoy': caja_hoy,
        'usuario_tienda': usuario_tienda,
        'tienda': tienda,
        'fecha': hoy
    }

    return render(request, 'venta_tienda/caja.html', context)


@tienda_login_required
def reimpresion_view(request):
    """Vista para reimpresión de recibos"""
    usuario_tienda = get_current_usuario_tienda(request)
    tienda = get_current_tienda(request)

    # Obtener solo bitácoras de tipo FEL (con factura) de la tienda
    bitacoras = BitacoraVenta.objects.filter(
        tienda=tienda,
        estado=1,
        tipo='FEL'  # Solo ventas con factura que se pueden reimprimir
    ).order_by('-fecha')[:50]  # Últimas 50 ventas FEL

    context = {
        'bitacoras': bitacoras,
        'usuario_tienda': usuario_tienda,
        'tienda': tienda,
    }

    return render(request, 'venta_tienda/reimpresion.html', context)


@tienda_login_required
def consulta_movimientos_view(request):
    """Vista para consultar todos los movimientos (FEL + Inventario)"""
    usuario_tienda = get_current_usuario_tienda(request)
    tienda = get_current_tienda(request)

    # Obtener TODAS las bitácoras de la tienda (FEL + Inventario)
    bitacoras = BitacoraVenta.objects.filter(
        tienda=tienda,
        estado=1  # Solo activas
    ).order_by('-fecha')[:50]  # Últimos 50 movimientos

    context = {
        'bitacoras': bitacoras,
        'usuario_tienda': usuario_tienda,
        'tienda': tienda,
    }

    return render(request, 'venta_tienda/consulta_movimientos.html', context)


@tienda_login_required
def detalle_bitacora(request, bitacora_id):
    """Vista para ver detalle de una bitácora específica"""
    usuario_tienda = get_current_usuario_tienda(request)
    tienda = get_current_tienda(request)

    bitacora = get_object_or_404(BitacoraVenta, id=bitacora_id, tienda=tienda)
    detalles = bitacora.detalles.all()

    context = {
        'bitacora': bitacora,
        'detalles': detalles,
        'usuario_tienda': usuario_tienda,
        'tienda': tienda,
    }

    return render(request, 'venta_tienda/detalle_bitacora.html', context)


@tienda_login_required
def generar_pdf_recibo(request, bitacora_id):
    """Vista para generar PDF de recibo"""
    usuario_tienda = get_current_usuario_tienda(request)
    tienda = get_current_tienda(request)

    bitacora = get_object_or_404(BitacoraVenta, id=bitacora_id, tienda=tienda)

    from .reportes import generar_pdf_recibo_inline
    return generar_pdf_recibo_inline(bitacora)


@tienda_login_required
def ultimas_ventas(request):
    """Obtener las últimas 10 ventas de la tienda"""
    try:
        tienda = get_current_tienda(request)
        ventas = BitacoraVenta.objects.filter(
            tienda=tienda,
            estado=1  # Solo ventas activas
        ).order_by('-fecha')[:10]

        ventas_list = []
        for venta in ventas:
            # Convertir a zona horaria local
            from django.utils import timezone
            fecha_local = timezone.localtime(venta.fecha)

            ventas_list.append({
                'numero_recibo': venta.numero_recibo,
                'fecha': fecha_local.strftime('%d/%m/%Y %H:%M'),
                'total': float(venta.total),
                'cliente': venta.cliente_nombre,
                'tipo': venta.tipo
            })

        return JsonResponse({'ventas': ventas_list})

    except Exception as e:
        return JsonResponse({'status': 'error', 'message': str(e)})


@tienda_login_required
def descargar_pdf_recibo(request, numero_recibo):
    """Vista para descargar PDF de recibo por número"""
    usuario_tienda = get_current_usuario_tienda(request)
    tienda = get_current_tienda(request)

    bitacora = get_object_or_404(BitacoraVenta, numero_recibo=numero_recibo, tienda=tienda)

    from .reportes import generar_pdf_recibo_inline
    return generar_pdf_recibo_inline(bitacora)


@tienda_login_required
def generar_pdf_venta(request, bitacora_id):
    """Vista para generar PDF inmediatamente después de la venta"""
    usuario_tienda = get_current_usuario_tienda(request)
    tienda = get_current_tienda(request)

    bitacora = get_object_or_404(BitacoraVenta, id=bitacora_id, tienda=tienda)

    from .reportes import generar_pdf_recibo_inline
    return generar_pdf_recibo_inline(bitacora)


@tienda_login_required
def inventario_view(request):
    """Vista para el módulo de inventario"""
    usuario_tienda = get_current_usuario_tienda(request)
    tienda = get_current_tienda(request)

    from Categoria.models import Categoria
    from .forms import BitacoraVentaForm

    categorias = Categoria.objects.all().order_by('nombre')
    form = BitacoraVentaForm()

    context = {
        'categorias': categorias,
        'form': form,
        'usuario_tienda': usuario_tienda,
        'tienda': tienda
    }

    return render(request, 'venta_tienda/inventario.html', context)


@tienda_login_required
@require_POST
def procesar_inventario(request):
    """Procesar el registro de inventario (sin generar PDF)"""
    try:
        data = json.loads(request.body)
        items = data.get('productos', [])
        observaciones = data.get('observaciones', '')

        if not items:
            return JsonResponse({'status': 'error', 'message': 'No hay items en el inventario'})

        usuario_tienda = get_current_usuario_tienda(request)
        tienda = get_current_tienda(request)

        # Crear la bitácora de inventario (sin información del cliente)
        bitacora = BitacoraVenta.objects.create(
            tienda=tienda,
            usuario_tienda=usuario_tienda,
            observaciones=observaciones,
            tipo='inventario'  # Tipo inventario
        )

        # Crear los detalles de la bitácora
        for item in items:
            item_id = item['id']
            cantidad = item['cantidad']

            # Determinar si es producto o receta
            if item_id.startswith('producto_'):
                producto_id = int(item_id.replace('producto_', ''))
                producto = get_object_or_404(Producto, id=producto_id)

                DetalleBitacora.objects.create(
                    bitacora=bitacora,
                    producto=producto,
                    cantidad=cantidad,
                    precio_unitario=producto.precio_venta
                )

            elif item_id.startswith('receta_'):
                receta_id = int(item_id.replace('receta_', ''))
                receta = get_object_or_404(Receta, id=receta_id)

                DetalleBitacora.objects.create(
                    bitacora=bitacora,
                    receta=receta,
                    cantidad=cantidad,
                    precio_unitario=receta.precio_venta
                )

        # Actualizar el total
        bitacora.actualizar_total()
        bitacora.save()

        return JsonResponse({
            'status': 'success',
            'message': 'Inventario registrado correctamente',
            'inventario_id': bitacora.id,
            'numero_recibo': bitacora.numero_recibo
        })

    except Exception as e:
        return JsonResponse({'status': 'error', 'message': f'Error al procesar inventario: {str(e)}'})
    

@tienda_login_required
@require_POST
def procesar_venta(request):
    """Procesar la venta usando BitacoraVenta (sin afectar stock)"""
    try:
        data = json.loads(request.body)
        items = data.get('productos', [])  # Puede contener productos y recetas
        observaciones = data.get('observaciones', '')
        cliente_data = data.get('cliente', {})

        if not items:
            return JsonResponse({'status': 'error', 'message': 'No hay items en la venta'})

        usuario_tienda = get_current_usuario_tienda(request)
        tienda = get_current_tienda(request)

        # Crear la bitácora de venta con información del cliente
        bitacora = BitacoraVenta.objects.create(
            tienda=tienda,
            usuario_tienda=usuario_tienda,
            observaciones=observaciones,
            cliente_nit=cliente_data.get('nit', 'CF'), # y los datos del usuario???????
            cliente_nombre=cliente_data.get('nombre', 'Consumidor Final'),
            cliente_direccion=cliente_data.get('direccion', 'Ciudad'),
            fecha=datetime.now()
        )
        # Crear los detalles de la bitácora
        for item in items:
            item_id = item['id']
            cantidad = item['cantidad']

            # Determinar si es producto o receta
            if item_id.startswith('producto_'):
                producto_id = int(item_id.replace('producto_', ''))
                producto = get_object_or_404(Producto, id=producto_id)

                DetalleBitacora.objects.create(
                    bitacora=bitacora,
                    producto=producto,
                    cantidad=cantidad,
                    precio_unitario=producto.precio_venta
                )

            elif item_id.startswith('receta_'):
                receta_id = int(item_id.replace('receta_', ''))
                receta = get_object_or_404(Receta, id=receta_id)

                DetalleBitacora.objects.create(
                    bitacora=bitacora,
                    receta=receta,
                    cantidad=cantidad,
                    precio_unitario=receta.precio_receta
                )

        # Actualizar el total de la bitácora
        bitacora.actualizar_total()
        fel(request,bitacora.id)

        if fel:
            b = BitacoraVenta.objects.get(id=bitacora.id)
            return JsonResponse({
                    'status': 'success',
                    'message': 'Venta procesada correctamente',
                    'venta_id': b.id,
                    'link': b.link,
                    'bitacora_id': b.id,
                    'numero_recibo': b.numero_recibo,
            })
      

    except Exception as e:
        return JsonResponse({'status': 'error', 'message': f'Error al procesar venta: {str(e)}'})




# FEL

def fel(request, id):
            factura = BitacoraVenta.objects.filter(id=id)
            factura_ver = BitacoraVenta.objects.get(id=id)
            detalle = DetalleBitacora.objects.filter(bitacora=id)
            cd = DetalleBitacora.objects.filter(bitacora=id).aggregate(
                cantidad=Sum('cantidad'))  # total de la venta
            total = DetalleBitacora.objects.filter(bitacora=id).aggregate(
                tot=Sum('subtotal'))  # total de la venta
            miventa = DetalleBitacora.objects.filter(bitacora=id).aggregate(
                t=Sum('subtotal'))  # total de la venta


            #BitacoraVenta.objects.filter(id=id).update(
            #    cliente_nit=request.POST["cliente-nit"], cliente_nombre=request.POST["cliente-nombre"], cliente_direccion=request.POST["cliente-direccion"], estado=1)
            # miventa = Venta.objects.filter(factura=id).values_list('nit','direccion','negocio')
            # facturar(request,id)

            # crear el DTE a Certificar

            dte_fel_a_certificar = InfileFel.fel_dte()  # aki mandamos el correlativo

            # crear el emisor
            emisor_fel = emisor.emisor()

            # crear el receptor
            receptor_fel = receptor.receptor()

            # crear los totales
            total_fel = InfileFel.totales()

            # totales impuestos
            totales_impuestos = InfileFel.total_impuesto()

            for datoscliente in BitacoraVenta.objects.filter(id=id):
                print(datoscliente.cliente_nit, datoscliente.cliente_nombre,
                      datoscliente.cliente_direccion)

            if datoscliente.tienda == 1:
                # Setear dirección del emisor
                emisor_fel.set_direccion(
                        'CALLE REAL 5-38 BARRIO ARRIBA ZONA 3 SAN JERONIMO, BAJA VERAPAZ \n',
                        '15001', 'TEL.', '0000-0000', 'GT'
                    )

                # Setear datos del emisor
                emisor_fel.set_datos_emisor(
                        'GEN', '2', '<EMAIL>', '11201377K',
                        'EL POLLO EXPRESS', 'SALGUERO_DEMO'
                    )

                # Setear dirección del receptor
                receptor_fel.set_direccion(
                        factura_ver.cliente_direccion, '14001', 'Baja Verapaz', 'guatemala', 'GT'
                    )
            elif datoscliente.tienda == 2:

                # Setear dirección del emisor
                emisor_fel.set_direccion(
                        '3 CALLE CANTON ILOM CHAJUL, QUICHE \n',
                        '14001', 'TEL.', '0000-0000', 'GT'
                    )

                # Setear datos del emisor
                emisor_fel.set_datos_emisor(
                        'GEN', '2', '<EMAIL>', '11201377K',
                        'EL POLLO EXPRESS', 'SALGUERO_DEMO'
                    )

                # Setear dirección del receptor
                receptor_fel.set_direccion(
                        factura_ver.cliente_direccion, '14001', 'Quiche', 'guatemala', 'GT'
                    )


            elif datoscliente.tienda == 3:

                # Setear dirección del emisor
                emisor_fel.set_direccion(
                        'BARRIO EL CENTRO 1-01 ZONA 1 RIO HONDO,ZACAPA \n',
                        '19003', 'TEL.', '0000-0000', 'GT'
                    )

                # Setear datos del emisor
                emisor_fel.set_datos_emisor(
                        'GEN', '3', '<EMAIL>', '11201377K',
                        'EL POLLO EXPRESS', 'SALGUERO_DEMO'
                    )

                # Setear dirección del receptor
                receptor_fel.set_direccion(
                        factura_ver.cliente_direccion, '19003', 'Zacapa', 'guatemala', 'GT'
                    )    

            elif datoscliente.tienda == 4:

                # Setear dirección del emisor
                emisor_fel.set_direccion(
                        'AVENIDA PUENTE FRENTE AL MERCADO SACAPULAS, QUICHE \n',
                        '14001', 'TEL.', '0000-0000', 'GT'
                    )

                # Setear datos del emisor
                emisor_fel.set_datos_emisor(
                        'GEN', '4', '<EMAIL>', '11201377K',
                        'EL POLLO EXPRESS', 'SALGUERO_DEMO'
                    )

                # Setear dirección del receptor
                receptor_fel.set_direccion(
                        factura_ver.cliente_direccion, '14001', 'Quiche', 'guatemala', 'GT'
                    )    

            elif datoscliente.tienda == 5:

                # Setear dirección del emisor
                emisor_fel.set_direccion(
                        '2 CALLE A BARRIO ARRIBA ZONA 1 ESTANZUELA,ZACAPA \n',
                        '19002', 'TEL.', '0000-0000', 'GT'
                    )

                # Setear datos del emisor
                emisor_fel.set_datos_emisor(
                        'GEN', '5', '<EMAIL>', '11201377K',
                        'EL POLLO EXPRESS', 'SALGUERO_DEMO'
                    )

                # Setear dirección del receptor
                receptor_fel.set_direccion(
                        factura_ver.cliente_direccion, '19002', 'Zacapa', 'guatemala', 'GT'
                    )       

            elif datoscliente.tienda == 6:

                # Setear dirección del emisor
                emisor_fel.set_direccion(
                        'BARRIO SAN SEBASTIAN SAN AGUSTIN ACASAGUASTLAN, EL PROGRESO \n',
                        '02003', 'TEL.', '0000-0000', 'GT'
                    )

                # Setear datos del emisor
                emisor_fel.set_datos_emisor(
                        'GEN', '5', '<EMAIL>', '11201377K',
                        'EL POLLO EXPRESS', 'SALGUERO_DEMO'
                    )

                # Setear dirección del receptor
                receptor_fel.set_direccion(
                        factura_ver.cliente_direccion, '02003', 'El Progreso', 'guatemala', 'GT'
                    )      

            elif datoscliente.tienda == 6:

                # Setear dirección del emisor
                emisor_fel.set_direccion(
                        'BARRIO SAN SEBASTIAN SAN AGUSTIN ACASAGUASTLAN, EL PROGRESO \n',
                        '02003', 'TEL.', '0000-0000', 'GT'
                    )

                # Setear datos del emisor
                emisor_fel.set_datos_emisor(
                        'GEN', '5', '<EMAIL>', '11201377K',
                        'EL POLLO EXPRESS', 'SALGUERO_DEMO'
                    )

                # Setear dirección del receptor
                receptor_fel.set_direccion(
                        factura_ver.cliente_direccion, '02003', 'El Progreso', 'guatemala', 'GT'
                    )                

            elif datoscliente.tienda == 7:

                # Setear dirección del emisor
                emisor_fel.set_direccion(
                        '3 CALLE A 7-47 ZPNA 1 CHICAMAN, QUICHE \n',
                        '14020', 'TEL.', '0000-0000', 'GT'
                    )

                # Setear datos del emisor
                emisor_fel.set_datos_emisor(
                        'GEN', '5', '<EMAIL>', '11201377K',
                        'POLLO EXPRESS', 'SALGUERO_DEMO'
                    )

                # Setear dirección del receptor
                receptor_fel.set_direccion(
                        factura_ver.cliente_direccion, '14020', 'Quiche', 'guatemala', 'GT'
                    )

            else:
                # Setear dirección del emisor
                emisor_fel.set_direccion(
                        '2 AVENIDA 3-022 CANTON BATZBACA ZONA 0 NEBAJ,QUICHE \n',
                        '14013', 'TEL.', '0000-0000', 'GT'
                    )

                # Setear datos del emisor
                emisor_fel.set_datos_emisor(
                        'GEN', '5', '<EMAIL>', '11201377K',
                        'POLLO EXPRESS', 'SALGUERO_DEMO'
                    )

                # Setear dirección del receptor
                receptor_fel.set_direccion(
                        factura_ver.cliente_direccion, '14013', 'Quiche', 'guatemala', 'GT'
                    )



            # Setear datos del receptor
            receptor_fel.set_datos_receptor(
                    '<EMAIL>', factura_ver.cliente_nit, factura_ver.cliente_nombre
                )
            
            # identificador unico del dte del cliente
            dte_fel_a_certificar.set_clave_unica(f'2022252{id}')  # no importa

            # setear datos generales
            dte_fel_a_certificar.set_datos_generales(
                'GTQ', f'{datoscliente.fecha}T00:00:00-06:00', 'FACT')  # fecha de venta

            # agregar los datos del emisor
            dte_fel_a_certificar.set_datos_emisor(emisor_fel)
            # agregar los datos del receptor
            dte_fel_a_certificar.set_datos_receptor(receptor_fel)

            # agregar las frases
            dte_fel_a_certificar.frase_fel.set_frase('1', '1')
            #dte_fel_a_certificar.frase_fel.set_frase('1', '2')
            # dte_fel_a_certificar.frase_fel.set_frase('1','1','20185687029123456789','2018-10-11')

            num = 1
            acutotal = 0
            acuiva = 0
            miventa = 0
            for item in DetalleBitacora.objects.filter(bitacora=id):

                num = num+1

                item_1 = InfileFel.item()

                item_1_impuesto = InfileFel.impuesto()

                # llenar el item con los datos necesarios
                item_1.set_numero_linea(num)  # esto falta
                item_1.set_bien_o_servicio('B')
                item_1.set_cantidad(item.cantidad)
                item_1.set_unidad_medida('UND')  # no se puede cambiar
                item_1.set_descripcion(item.producto.nombre)
                item_1.set_precio_unitario(item.producto.precio_venta)
                item_1.set_precio(item.cantidad*item.precio_unitario)
                item_1.set_descuento(0)
                item_1.set_total(item.subtotal)

                grav = round((item.subtotal/112)*100, 2)
                iva = round((grav*12)/100, 2)

                # llenar los impuestos del item
                item_1_impuesto.set_monto_impuesto(iva)
                item_1_impuesto.set_monto_gravable(grav)
                item_1_impuesto.set_codigo_unidad_gravable(1)
                item_1_impuesto.set_nombre_corto('IVA')
                item_1.set_impuesto(item_1_impuesto)

                acutotal = acutotal + grav
                acuiva = acuiva + iva
                miventa = miventa + item.subtotal

                # Agregar el item 1
                dte_fel_a_certificar.agregar_item(item_1)

                # agregar el gran total

            total_fel.set_gran_total(miventa)

            # agregar datos del gran total de impuestos

            totales_impuestos.set_nombre_corto('IVA')
            totales_impuestos.set_total_monto_impuesto(acuiva)

            total_fel.set_total_impuestos(totales_impuestos)

            # agregar los totales
            dte_fel_a_certificar.agregar_totales(total_fel)

          
            print("#### CERTIFICANDO FACTURA ####")

            # contingencia
            # dte_fel_a_certificar.set_acceso('123123')

            # agregando el tipo de personeria
            # dte_fel_a_certificar.set_tipo_personeria('2')

            # realizar el llamado a la certificada
            certificacion_fel = dte_fel_a_certificar.certificar()
            if (certificacion_fel["resultado"]):
                print("UUID:" + certificacion_fel["uuid"])
                print("FECHA:" + certificacion_fel["fecha"])
                print("SERIE:" + certificacion_fel["serie"])
                print("numero:" + str(certificacion_fel["numero"]))
                total = DetalleBitacora.objects.filter(bitacora=id).aggregate(
                    tot=Sum('subtotal'))  # total de la venta
                messages.info(
                    request, f"https://report.feel.com.gt/ingfacereport/ingfacereport_documento?uuid={certificacion_fel['uuid']}")
                BitacoraVenta.objects.filter(id=id).update(fecha_fel=str(certificacion_fel['fecha']), estado=1, link=f"https://report.feel.com.gt/ingfacereport/ingfacereport_documento?uuid={certificacion_fel['uuid']}", anula=certificacion_fel['uuid'], serie=certificacion_fel['serie'], numero=certificacion_fel['numero'])
                # Venta.objects.filter(factura=id).update(link=f"https://report.feel.com.gt/ingfacereport/ingfacereport_documento?uuid={certificacion_fel['uuid']}")
                # messages.info(request,factura_ver.link)
                return redirect('/procesar-venta/')

            else:
                print("No pudo ser certificada")
                print("Descripcion: " + certificacion_fel["descripcion"])

                for error_fel in certificacion_fel["descripcion_errores"]:
                    print("Mensaje Error: " + error_fel["fuente"])
                    print("fuente: " + error_fel["mensaje_error"])
                    print("categoria: " + error_fel["categoria"])
                    print("numeral: " + error_fel["numeral"])
                    print("validacion: " + error_fel["validacion"])

      

        ###################################################################################################################################

    # return render(request,"PagoApp/pagoefectivo.html",{'factura':factura,'detalle':detalle,'cd':cd,'total':total,'id':id})



def anularfel(request, id):
        print('#### Anulando ####')
        datoscliente = BitacoraVenta.objects.get(id=id)

        dte_fel_a_anular = InfileFel.fel_dte()

        # realizar el llamado a la certificada
        certificacion_fel = dte_fel_a_anular.anular(str(datoscliente.fecha_fel), '11201377K', str(
            datoscliente.fecha_fel), datoscliente.cliente_nit, datoscliente.anula, datoscliente.serie)
        if (certificacion_fel["resultado"]):
            print("UUID:" + certificacion_fel["uuid"])
            print("FECHA:" + certificacion_fel["fecha"])
            print("SERIE:" + certificacion_fel["serie"])
            print("numero:" + str(certificacion_fel["numero"]))
        else:
            print("No pudo ser certificada")
            print("Descripcion: " + certificacion_fel["descripcion"])

            for error_fel in certificacion_fel["descripcion_errores"]:
                print("Mensaje Error: " + error_fel["fuente"])
                print("fuente: " + error_fel["mensaje_error"])
                print("categoria: " + error_fel["categoria"])
                print("numeral: " + error_fel["numeral"])
                print("validacion: " + error_fel["validacion"])

        total = DetalleBitacora.objects.all().filter(bitacora=id).aggregate(
            tot=Sum('subtotal'))  # total de la venta
        BitacoraVenta.objects.filter(id=id).update(fecha_fel=datoscliente.fecha_fel, estado=2)
        #messages.success(request, 'Anulacion FEL Exitosa')
        return redirect('/reimpresion/')    