from django import forms
from django.forms import ModelForm
from Cierre.models import Cierre

ESTADO = (
(0,'Baja'),
(1,'Activo'),
)

#forms.Select(attrs={'class': 'selectpicker form-control','data-style':'btn-outline-info','placeholder':'Tipo','require':True},choices=TIPO),
class CierreForm(ModelForm):

    class Meta:
        model = Cierre
        fields = ['caja','deposito','boleta','pollo','boleta_pollo','libras','tienda']
        
        labels = {'caja':'Dinero en Caja','deposito':'Depositado','boleta':'Boleta Deposito'
                  ,'pollo':'Total Pollo Rey','boleta_pollo':'Deposito Pollo Rey','libras':'Libras Pollo','liquido':'Liquido','tienda':'Tienda'}     

        widgets = { 

                 'caja':forms.TextInput(attrs={'class': 'form-control','placeholder':'0.00','id':'caja','require':True}),
                 'deposito':forms.TextInput(attrs={'class': 'form-control','placeholder':'0.00','id':'deposito','autofocus': True,'require':True}),
                 'boleta':forms.TextInput(attrs={'class': 'form-control','placeholder':'Boleta Deposito','autofocus': True,'require':True}),
                 'pollo':forms.TextInput(attrs={'type':'number','class': 'form-control','placeholder':'0.00','type': 'text','require':False}),
                 'boleta_pollo':forms.TextInput(attrs={'class': 'form-control','placeholder':'Boleta Deposito Pollo','autofocus': True,'require':True}),
                 'libras':forms.TextInput(attrs={'class': 'form-control','placeholder':'0.00','autofocus': True,'require':True}),
                 'tienda':forms.Select(attrs={'class': 'selectpicker form-control','data-style':'btn-outline-info','placeholder':'Tienda','require':True}),
        }         


class UpdateCierreForm(ModelForm):

    class Meta:
        model = Cierre
        fields = ['ventas','gastos','caja','deposito','boleta','pollo','boleta_pollo','libras','liquido','tienda']
        
        labels = {'ventas':'Ventas del Dia','gastos':'Gastos del Dia','caja':'Dinero en Caja','deposito':'Depositado','boleta':'Boleta Deposito'
                  ,'pollo':'Total Pollo Rey','boleta_pollo':'Deposito Pollo Rey','libras':'Libras Pollo','liquido':'Liquido','tienda':'Tienda'}     

        widgets = { 

                 'ventas':forms.TextInput(attrs={'type':'number','class': 'form-control','placeholder':'0','id':'ventas','autofocus': True,'require':True}),
                 'gastos':forms.TextInput(attrs={'class': 'form-control','placeholder':'0.00','id':'gastos','autofocus': True,'require':True}),
                 'caja':forms.TextInput(attrs={'class': 'form-control','placeholder':'0.00','id':'caja','autofocus': True,'require':True}),
                 'deposito':forms.TextInput(attrs={'class': 'form-control','placeholder':'0.00','id':'deposito','autofocus': True,'require':True}),
                 'boleta':forms.TextInput(attrs={'class': 'form-control','placeholder':'Boleta Deposito','id':'precio','autofocus': True,'require':True}),
                 'pollo':forms.TextInput(attrs={'type':'number','class': 'form-control','id':'total','placeholder':'0.00','type': 'text','require':False}),
                 'liquido':forms.TextInput(attrs={'type':'number','class': 'form-control','id':'liquido','placeholder':'0.00','type': 'text','require':False,'readonly':True}),
                 'boleta_pollo':forms.TextInput(attrs={'class': 'form-control','placeholder':'Boleta Deposito Pollo','id':'precio','autofocus': True,'require':True}),
                 'libras':forms.TextInput(attrs={'class': 'form-control','placeholder':'0.00','id':'precio','autofocus': True,'require':True}),
                 'tienda':forms.Select(attrs={'class': 'selectpicker form-control','data-style':'btn-outline-info','placeholder':'Tienda','require':True}),
        }         