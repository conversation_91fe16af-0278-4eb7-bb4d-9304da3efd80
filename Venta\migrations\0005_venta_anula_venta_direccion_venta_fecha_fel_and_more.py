# Generated by Django 4.2.4 on 2025-08-06 02:47

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('Venta', '0004_alter_venta_estado'),
    ]

    operations = [
        migrations.AddField(
            model_name='venta',
            name='anula',
            field=models.CharField(blank=True, max_length=850, null=True),
        ),
        migrations.AddField(
            model_name='venta',
            name='direccion',
            field=models.CharField(blank=True, max_length=850, null=True),
        ),
        migrations.AddField(
            model_name='venta',
            name='fecha_fel',
            field=models.CharField(blank=True, max_length=550, null=True),
        ),
        migrations.AddField(
            model_name='venta',
            name='link',
            field=models.CharField(blank=True, max_length=250, null=True),
        ),
        migrations.AddField(
            model_name='venta',
            name='nit',
            field=models.Char<PERSON>ield(blank=True, default='CF', max_length=15, null=True),
        ),
        migrations.AddField(
            model_name='venta',
            name='nombre',
            field=models.CharField(blank=True, max_length=250, null=True),
        ),
        migrations.AddField(
            model_name='venta',
            name='numero',
            field=models.BigIntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='venta',
            name='serie',
            field=models.CharField(blank=True, max_length=550, null=True),
        ),
        migrations.AddField(
            model_name='venta',
            name='tipo',
            field=models.CharField(blank=True, max_length=250, null=True),
        ),
    ]
