# Generated by Django 5.0.4 on 2024-11-11 00:50

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('Dash', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='<PERSON><PERSON><PERSON>',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('ventas', models.DecimalField(decimal_places=2, default=0.0, max_digits=12)),
                ('gastos', models.DecimalField(decimal_places=2, default=0.0, max_digits=12)),
                ('caja', models.DecimalField(decimal_places=2, default=0.0, max_digits=12)),
                ('deposito', models.DecimalField(decimal_places=2, default=0.0, max_digits=12)),
                ('boleta', models.CharField(default='0', max_length=50)),
                ('pollo', models.DecimalField(decimal_places=2, default=0.0, max_digits=12)),
                ('boleta_pollo', models.CharField(default='0', max_length=50)),
                ('libras', models.DecimalField(decimal_places=2, default=0.0, max_digits=12)),
                ('liquido', models.DecimalField(decimal_places=2, default=0.0, max_digits=12)),
                ('fecha', models.DateTimeField(auto_now_add=True)),
                ('fecha_mod', models.DateTimeField(auto_now_add=True)),
                ('estado', models.IntegerField(default=1)),
                ('tienda', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='Dash.tienda')),
                ('usuario', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['id'],
            },
        ),
    ]
