{% extends 'Base/base.html' %}

{% block title %}Actualizacion de Puesto{% endblock %}

{% block content %}

<div class="container">
    <br>
    <a href="{% url 'Producto' %}" class="btn btn-info">Ingreso Producto</a>
  

  </div>

<div class="col-12">
    <div class="card my-4">
<div class="container" style="background-color: white;"><br>
    <div class="row">
        <div class="col-md-6">
            <h3>FORMULARIO MODIFICACION DE PUESTO</h3>
        </div>
        <div class="col-md-6" align="right">
            <a href="{% url 'Inicio' %}"><i class="material-icons opacity-10" style="font-size: 35px;"
                    title="Inicio">INICIO</i></a>
        </div>
    </div>
    <form action="#" method="POST" enctype="multipart/form-data">
        {% csrf_token %}
        <div class="row">
            <div class="col-md-4">
                <div class="input-group input-group-outline my-3">
                    <label class="form-label">Nombre del Puesto</label>
                    {{form.nombre}}
                </div>
            </div>
            <div class="col-md-4">
                <div class="input-group input-group-outline my-3">
                    <label class="form-label">Descripcion</label>
                   {{form.descripcion}}
                </div>
            </div>
            <div class="col-md-4">
                <div class="input-group input-group-outline my-3">
                    <label class="form-label">Sueldo</label>
                    {{form.sueldo}}
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-6">
                <div class="input-group input-group-outline my-3">
                    <input type="text" class="form-control" name="fecha" value="{% now 'd-m-Y' %}" readonly>
                </div>
            </div>
            <div class="col-md-6">
                <div class="input-group input-group-outline my-3">
                    <input type="text" class="form-control" name="usuario" value="{{user.username}}" readonly>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-6">
                <button type="submit" class="btn btn-success">Guardar</button>
                <button type="reset" class="btn btn-danger">Cancelar</button>
            </div>
        </div>
    </form>
</div>

{% endblock %}