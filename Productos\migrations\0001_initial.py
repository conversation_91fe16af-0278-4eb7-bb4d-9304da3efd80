# Generated by Django 5.0.4 on 2024-11-10 23:25

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('Dash', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Producto',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('nombre', models.CharField(blank=True, default='', max_length=550, null=True)),
                ('medida', models.CharField(blank=True, default='', max_length=550, null=True)),
                ('inicio', models.IntegerField(default=0)),
                ('ingreso', models.IntegerField(default=0)),
                ('salida', models.IntegerField(default=0)),
                ('final', models.IntegerField(default=0)),
                ('estado', models.IntegerField(default=1)),
                ('fecha', models.DateTimeField(auto_now_add=True)),
                ('fecha_mod', models.DateTimeField(auto_now_add=True)),
                ('tienda', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='Dash.tienda')),
                ('usuario', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['id'],
            },
        ),
    ]
