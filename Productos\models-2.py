from django.db import models
from user.models import User
from Dash.models import Tienda

class Producto(models.Model):
    nombre = models.CharField(max_length=550,blank=True,null=True,default='')
    medida = models.CharField(max_length=550,blank=True,null=True,default='')
    inicio = models.IntegerField(blank=False,null=False,default=0)
    ingreso = models.IntegerField(blank=False,null=False,default=0)
    salida = models.IntegerField(blank=False,null=False,default=0)
    final = models.IntegerField(blank=False,null=False,default=0)
    precio_compra = models.DecimalField(max_digits=12,decimal_places=2,blank=True,null=True,default=0.00)
    precio_venta = models.DecimalField(max_digits=12,decimal_places=2,blank=True,null=True,default=0.00)
    total_venta = models.DecimalField(max_digits=12,decimal_places=2,blank=True,null=True,default=0.00)
    estado = models.IntegerField(blank=False,null=False,default=1)
    tienda = models.ForeignKey(Tienda,blank=False,null=False,on_delete=models.CASCADE)
    fecha = models.DateField(blank=False,null=False,auto_now_add=True)
    fecha_mod = models.DateTimeField(blank=False,null=False,auto_now_add=True)
    usuario = models.ForeignKey(User,blank=False,null=False,on_delete=models.CASCADE)

    class Meta:
        ordering = ["id"]

    def __str__(self):
        return self.nombre
    

    
    



