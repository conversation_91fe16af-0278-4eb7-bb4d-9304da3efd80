# Generated by Django 4.2.16 on 2025-07-25 03:29

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('Dash', '0001_initial'),
        ('Receta', '0002_receta_imagen'),
        ('Productos', '0011_producto_imagen'),
    ]

    operations = [
        migrations.CreateModel(
            name='BitacoraVenta',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('fecha', models.DateTimeField(auto_now_add=True)),
                ('total', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('observaciones', models.TextField(blank=True, null=True)),
                ('numero_recibo', models.CharField(max_length=50, unique=True)),
                ('estado', models.IntegerField(default=1)),
                ('tipo', models.CharField(choices=[('FEL', 'Venta con Factura'), ('inventario', 'Registro de Inventario')], default='FEL', max_length=20)),
                ('cliente_nit', models.CharField(blank=True, default='C/F', max_length=20)),
                ('cliente_nombre', models.CharField(blank=True, default='Consumidor Final', max_length=200)),
                ('cliente_direccion', models.CharField(blank=True, default='Ciudad', max_length=300)),
                ('tienda', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='Dash.tienda')),
            ],
            options={
                'verbose_name': 'Bitácora de Venta',
                'verbose_name_plural': 'Bitácoras de Ventas',
                'ordering': ['-fecha'],
            },
        ),
        migrations.CreateModel(
            name='UsuarioTienda',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('username', models.CharField(max_length=150, unique=True)),
                ('nombre', models.CharField(max_length=100)),
                ('apellido', models.CharField(max_length=100)),
                ('email', models.EmailField(blank=True, max_length=254, null=True)),
                ('telefono', models.CharField(blank=True, max_length=20, null=True)),
                ('dpi', models.CharField(max_length=20, unique=True)),
                ('password', models.CharField(max_length=128)),
                ('fecha_creacion', models.DateTimeField(auto_now_add=True)),
                ('activo', models.BooleanField(default=True)),
                ('tienda', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='Dash.tienda')),
            ],
            options={
                'verbose_name': 'Usuario de Tienda',
                'verbose_name_plural': 'Usuarios de Tienda',
                'ordering': ['nombre', 'apellido'],
            },
        ),
        migrations.CreateModel(
            name='DetalleBitacora',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('cantidad', models.IntegerField(default=1)),
                ('precio_unitario', models.DecimalField(decimal_places=2, max_digits=12)),
                ('subtotal', models.DecimalField(decimal_places=2, max_digits=12)),
                ('bitacora', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='detalles', to='VentaTienda.bitacoraventa')),
                ('producto', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='Productos.producto')),
                ('receta', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='Receta.receta')),
            ],
            options={
                'verbose_name': 'Detalle de Bitácora',
                'verbose_name_plural': 'Detalles de Bitácoras',
            },
        ),
        migrations.AddField(
            model_name='bitacoraventa',
            name='usuario_tienda',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='VentaTienda.usuariotienda'),
        ),
        migrations.CreateModel(
            name='CajaTienda',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('fecha', models.DateField()),
                ('dinero_inicial', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('observaciones', models.TextField(blank=True, null=True)),
                ('fecha_registro', models.DateTimeField(auto_now_add=True)),
                ('tienda', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='Dash.tienda')),
                ('usuario_tienda', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='VentaTienda.usuariotienda')),
            ],
            options={
                'verbose_name': 'Caja de Tienda',
                'verbose_name_plural': 'Cajas de Tiendas',
                'ordering': ['-fecha'],
                'unique_together': {('tienda', 'fecha')},
            },
        ),
    ]
