{% extends 'Base/base.html' %}

{% block title %}Nueva Receta{% endblock %}

{% block content %}

<br>
<div class="container" style="margin-top: 5px; max-width: 90%;">
  <br>
  <a href="{% url 'ListaReceta' %}" class="btn btn-info">Lista de Recetas</a> 
      
  <div class="card my-4">
    <div class="container" style="background-color: white;"><br>
      <div class="row">
        <div class="col-md-6">
          <h3>FORMULARIO INGRESO NUEVA RECETA</h3>
        </div>
      </div>

      {% if messages %}
        {% for message in messages %}
          <script>
            Swal.fire({
              title: 'Hecho!',
              text: '{{ message }}',
              icon: 'success',
              confirmButtonText: 'Ok'
            })
          </script>
        {% endfor %}
      {% endif %}

      <form action="#" method="POST" enctype="multipart/form-data">
        {% csrf_token %}
        
        <div class="row">
          <div class="col-md-3">
            <label for="">Nombre de Receta</label>
            {{ form.nombre }}
          </div>
          <div class="col-md-3">
            <label for="">Descripción de Receta</label>
            {{ form.descripcion }}
          </div>
          <div class="col-md-3">
            <label for="">Tienda</label>
            {{ form.tienda }}
          </div>
          <div class="col-md-3">
            <label for="">Cantidad a Elaborar</label>
            {{ form.cantidad }}
          </div>
        </div>
        <br> 

        <div class="row">
          <div class="col-md-3">
            <label for="">Tiempo de Elaboración en Días</label>
            {{ form.tiempo }}
          </div>
          <div class="col-md-3">
            <label for="">Precio de Receta</label>
            {{ form.precio_receta }}
          </div>
          <div class="col-md-3">
            <label for="">Imagen</label>
            {{ form.imagen }}
          </div>
          <div class="col-md-3">
            <label for="">Fecha Sistema</label>
            <input type="text" class="form-control" readonly value="{% now 'd-m-Y' %}">
          </div>
      
          <div class="col-md-3">
            <label for="">Usuario</label>
            <input type="text" class="form-control" readonly value="{{ user.username }}">
          </div>
        </div>
        <br>

        <div class="row">
          <div class="col-md-3">
            <button type="submit" class="btn btn-success btn-md">Guardar</button>
          </div>
        </div>
        <br> 
      </form>
    </div>
  </div>
</div>

{% endblock %}
