# Generated by Django 5.0.4 on 2024-11-11 01:54

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Facturar',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('facturar', models.DecimalField(decimal_places=2, default=0.0, max_digits=12)),
                ('fecha', models.DateField()),
                ('fecha_sistema', models.DateField(auto_now_add=True)),
                ('fecha_mod', models.DateTimeField(auto_now_add=True)),
                ('estado', models.IntegerField(default=1)),
                ('usuario', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['id'],
            },
        ),
    ]
