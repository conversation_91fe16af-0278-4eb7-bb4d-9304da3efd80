{% extends 'Base/base.html' %}
{% load static %}

{% block content %}

<!--{% if messages %}
{% for message in messages %}
<script>
  Swal.fire({
    "title": "Informacion Sistema",
    "text": "{{ message }}",
    "icon": "{{ message.tags }}"
  })
</script>
{% endfor %}
{% endif %}-->

<div class="container">
  <h3>FORMULARIO DE ACTUALIZACION PRODUCTOS</h3>
  <form action="#" method="POST" enctype="multipart/form-data">
    {% csrf_token %}
    <div class="row">

      <div class="col-md-4">
        <label>{{form.nombre.label_tag}}</label>
        {{form.nombre}}
      </div>
      <div class="col-md-4">
        <label>{{form.medida.label_tag}}</label>
        {{form.medida}}
      </div>
      <div class="col-md-4">
        <label>{{form.tienda.label_tag}}</label>
        {{form.tienda}}
      </div>
      
  
  
    </div><br>
  
    <div class="row">
      <div class="col-md-3">
        <label>{{form.inicio.label_tag}}</label>
        {{form.inicio}}
      </div>
      <div class="col-md-3">
        <label>{{form.ingreso.label_tag}}</label>
        {{form.ingreso}}
      </div>
      <div class="col-md-3">
        <label>{{form.salida.label_tag}}</label>
        {{form.salida}}
      </div>
      <div class="col-md-3">
        <label>{{form.final.label_tag}}</label>
        {{form.final}}
      </div>
      
  
  
    </div><br>
  
    <div class="row">
      <div class="col-md-3">
        <label>{{form.precio_compra.label_tag}}</label>
        {{form.precio_compra}}
      </div>
      <div class="col-md-3">
        <label>{{form.precio_venta.label_tag}}</label>
        {{form.precio_venta}}
      </div>
      <div class="col-md-3">
        <label>{{form.total_venta.label_tag}}</label>
        {{form.total_venta}}
      </div>
      
  
  
    </div><br>

    <div class="row">
      <div class="col-md-4">
        <button type="submit" class="btn btn-success">Guardar</button>
        <!-- Asegúrate de incluir el parámetro 'tienda' en el enlace de 'Cancelar' -->
        <a href="{% url 'ListaProducto' %}?tienda={{ tienda_seleccionada }}" class="btn btn-danger">Cancelar</a>
      </div>
    </div><br>
  </form>
</div>

<script>
  const inicio = document.getElementById("inicio");
  const ingreso = document.getElementById("ingreso");
  const salida = document.getElementById("salida");
  const final = document.getElementById("final");

  const venta = document.getElementById("venta");
  const total_venta = document.getElementById("total_venta");

  // obtener el valor de un campo como flotante (o 0 si esta vacio)
  function getIntValue(element) {
      return parseFloat(element.value) || 0;
  }

  //  actualizr el campo "final"
  function updateFinal() {
      final.value = getIntValue(inicio) + getIntValue(ingreso) - getIntValue(salida);
  }

  // actualzar el campo "total_venta"
  function updateTotalVenta() {
      total_venta.value = getIntValue(salida) * getIntValue(venta);
  }

  // eventos de cambio en los campos
  inicio.addEventListener("input", updateFinal);
  ingreso.addEventListener("input", updateFinal);
  salida.addEventListener("input", () => {
      updateFinal();
      updateTotalVenta();
  });
  venta.addEventListener("input", updateTotalVenta);
</script>


{% endblock %}
