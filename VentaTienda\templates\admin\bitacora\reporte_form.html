{% extends 'Base/base.html' %}

{% block title %}Generar Reporte de Movimientos{% endblock %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h1><i class="fas fa-file-pdf"></i> Generar Reporte de Movimientos</h1>
                <a href="{% url 'admin_empleados:bitacora_lista' %}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Volver a Bitácora
                </a>
            </div>
        </div>
    </div>

    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-cog"></i> Configuración del Reporte</h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        <strong>Información:</strong> Este reporte generará un PDF con todos los movimientos de la tienda seleccionada en la fecha especificada, 
                        incluyendo un resumen general, desglose por tipo (FEL e Inventario) y totales.
                    </div>

                    <form method="POST">
                        {% csrf_token %}
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="tienda_id" class="form-label">
                                    <i class="fas fa-store"></i> Seleccionar Tienda <span class="text-danger">*</span>
                                </label>
                                <select name="tienda_id" id="tienda_id" class="form-select" required>
                                    <option value="">-- Seleccione una tienda --</option>
                                    {% for tienda in tiendas %}
                                        <option value="{{ tienda.id }}">{{ tienda.nombre }}</option>
                                    {% endfor %}
                                </select>
                                <div class="form-text">Seleccione la tienda para la cual desea generar el reporte.</div>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="fecha_reporte" class="form-label">
                                    <i class="fas fa-calendar"></i> Fecha del Reporte <span class="text-danger">*</span>
                                </label>
                                <input type="date" name="fecha_reporte" id="fecha_reporte" class="form-control" 
                                       value="{{ fecha_hoy }}" required max="{{ fecha_hoy }}">
                                <div class="form-text">Seleccione la fecha para la cual desea generar el reporte.</div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12">
                                <div class="card bg-light">
                                    <div class="card-body">
                                        <h6 class="card-title"><i class="fas fa-list"></i> El reporte incluirá:</h6>
                                        <ul class="mb-0">
                                            <li><strong>Resumen General:</strong> Total de movimientos, cantidad de productos vendidos y monto total</li>
                                            <li><strong>Desglose por Producto:</strong> Lista completa de todos los productos vendidos con cantidades</li>
                                            <li><strong>Ventas FEL:</strong> Desglose específico de ventas con factura</li>
                                            <li><strong>Registro de Inventario:</strong> Desglose específico de registros sin factura</li>
                                            <li><strong>Totales:</strong> Resumen financiero del día</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row mt-4">
                            <div class="col-12 text-center">
                                <button type="submit" class="btn btn-success btn-lg">
                                    <i class="fas fa-file-pdf"></i> Generar Reporte PDF
                                </button>
                                <a href="{% url 'admin_empleados:bitacora_lista' %}" class="btn btn-outline-secondary btn-lg ms-3">
                                    <i class="fas fa-times"></i> Cancelar
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Información adicional -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card border-warning">
                <div class="card-header bg-warning text-dark">
                    <h6 class="mb-0"><i class="fas fa-exclamation-triangle"></i> Notas Importantes</h6>
                </div>
                <div class="card-body">
                    <ul class="mb-0">
                        <li>El reporte se genera en formato PDF y se abrirá en una nueva ventana</li>
                        <li>Solo se incluyen movimientos del día seleccionado</li>
                        <li>Los movimientos anulados no se incluyen en el reporte</li>
                        <li>El reporte está optimizado para impresión en papel tamaño carta</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Validación del formulario
    $('form').on('submit', function(e) {
        const tienda = $('#tienda_id').val();
        const fecha = $('#fecha_reporte').val();
        
        if (!tienda || !fecha) {
            e.preventDefault();
            Swal.fire({
                icon: 'error',
                title: 'Campos Requeridos',
                text: 'Por favor seleccione la tienda y la fecha para generar el reporte.'
            });
            return false;
        }
        
        // Mostrar loading
        Swal.fire({
            title: 'Generando Reporte...',
            text: 'Por favor espere mientras se genera el PDF',
            allowOutsideClick: false,
            allowEscapeKey: false,
            showConfirmButton: false,
            didOpen: () => {
                Swal.showLoading();
            }
        });
        
        // Cerrar loading después de un tiempo (el PDF se abrirá en nueva ventana)
        setTimeout(() => {
            Swal.close();
        }, 3000);
    });
    
    // Actualizar fecha máxima
    const today = new Date().toISOString().split('T')[0];
    $('#fecha_reporte').attr('max', today);
});
</script>
{% endblock %}
