from django import forms
from django.contrib.auth.forms import AuthenticationForm
from .models import UsuarioTienda, BitacoraVenta, CajaTienda


class LoginTiendaForm(forms.Form):
    """Formulario de login para usuarios de tienda"""
    username = forms.CharField(
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Usuario',
            'required': True
        })
    )
    password = forms.CharField(
        widget=forms.PasswordInput(attrs={
            'class': 'form-control',
            'placeholder': 'Contraseña',
            'required': True
        })
    )


class BitacoraVentaForm(forms.ModelForm):
    """Formulario para bitácora de ventas"""
    class Meta:
        model = BitacoraVenta
        fields = ['observaciones']
        widgets = {
            'observaciones': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'Observaciones adicionales...'
            })
        }


class CajaTiendaForm(forms.ModelForm):
    """Formulario para registro de caja diaria"""
    class Meta:
        model = CajaTienda
        fields = ['dinero_inicial', 'observaciones']
        widgets = {
            'dinero_inicial': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.01',
                'min': '0',
                'placeholder': '0.00'
            }),
            'observaciones': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'Observaciones sobre la caja...'
            })
        }
        labels = {
            'dinero_inicial': 'Dinero Inicial (Q)',
            'observaciones': 'Observaciones'
        }


class UsuarioTiendaForm(forms.ModelForm):
    """Formulario para crear/editar usuarios de tienda"""
    password = forms.CharField(
        widget=forms.PasswordInput(attrs={
            'class': 'form-control',
            'placeholder': 'Contraseña'
        }),
        label='Contraseña',
        required=False  # No requerido para edición
    )

    class Meta:
        model = UsuarioTienda
        fields = ['username', 'nombre', 'apellido', 'email', 'telefono', 'dpi', 'tienda']
        widgets = {
            'username': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Usuario único'}),
            'nombre': forms.TextInput(attrs={'class': 'form-control'}),
            'apellido': forms.TextInput(attrs={'class': 'form-control'}),
            'email': forms.EmailInput(attrs={'class': 'form-control'}),
            'telefono': forms.TextInput(attrs={'class': 'form-control'}),
            'dpi': forms.TextInput(attrs={'class': 'form-control'}),
            'tienda': forms.Select(attrs={'class': 'form-control'}),
        }
        labels = {
            'username': 'Usuario',
            'nombre': 'Nombre',
            'apellido': 'Apellido',
            'email': 'Correo Electrónico',
            'telefono': 'Teléfono',
            'dpi': 'DPI',
            'tienda': 'Tienda',
        }

    def clean_password(self):
        """Validar contraseña"""
        password = self.cleaned_data.get('password')

        # Si es un nuevo usuario (no tiene instance.pk), la contraseña es requerida
        if not self.instance.pk and not password:
            raise forms.ValidationError('La contraseña es requerida para nuevos usuarios.')

        return password

    def save(self, commit=True):
        """Guardar usuario con contraseña encriptada"""
        usuario = super().save(commit=False)

        # Solo establecer contraseña si se proporcionó
        if self.cleaned_data.get('password'):
            usuario.set_password(self.cleaned_data['password'])

        if commit:
            usuario.save()
        return usuario